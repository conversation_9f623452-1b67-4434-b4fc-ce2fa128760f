#!/bin/bash

# DAL 生成和下载脚本
# 参考 sync-influencer-videos-from-feishu.mjs 的 login 函数实现

set -e  # 遇到错误立即退出

# 配置
API_BASE_URL="https://ec-ops.blueorigin.work"
USERNAME="<EMAIL>"
PASSWORD="BO2024$+2"
GITLAB_REPO_URL="https://git.blueorigin.work/e-commerce/gorm-generated-dal.git"
PROJECT_ROOT="$(pwd)"
DAL_DIR="$PROJECT_ROOT/dal"

# 默认站点列表
DEFAULT_SITES="ops,admin,fulfillment,membership,ohsome"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        log_info "Please install jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "git is required but not installed"
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# 登录函数 - 参考 sync-influencer-videos-from-feishu.mjs
login() {
    log_info "Logging in to ec-ops..." >&2

    local response=$(curl -s -X POST "$API_BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

    # 检查响应是否为空
    if [ -z "$response" ]; then
        log_error "Empty response from login API" >&2
        return 1
    fi

    # 检查是否包含 token
    local token=$(echo "$response" | jq -r '.token // empty')
    local user_id=$(echo "$response" | jq -r '.id // empty')
    local username=$(echo "$response" | jq -r '.name // empty')

    if [ -z "$token" ] || [ "$token" = "null" ]; then
        log_error "Login failed. Response: $response" >&2
        return 1
    fi

    log_success "Login successful" >&2
    # 返回 token、user_id 和 username，参考 download-language.sh 格式
    printf "%s|%s|%s" "$token" "$user_id" "$username"
}

# 调用 DAL 生成 API
generate_dal() {
    local credentials="$1"
    local sites="$2"
    local token=$(printf "%s" "$credentials" | cut -d'|' -f1)
    local user_id=$(printf "%s" "$credentials" | cut -d'|' -f2)
    local username=$(printf "%s" "$credentials" | cut -d'|' -f3)

    # 将逗号分隔的 sites 转换为 JSON 数组
    local sites_json=$(echo "$sites" | sed 's/,/","/g' | sed 's/^/"/' | sed 's/$/"/')
    local request_body="{\"databaseNames\":[$sites_json]}"

    log_info "Calling DAL generation API for sites: $sites"
    log_info "Request body: $request_body"

    # 参考 download-language.sh 的请求头设置，并添加必需的请求体
    local response=$(curl -s -X POST "$API_BASE_URL/api/ops/gorm/dal/generate" \
        -H 'sec-ch-ua-platform: "macOS"' \
        -H "Authorization: $token" \
        -H "ec-user-id: $user_id" \
        -H "Referer: $API_BASE_URL/ops/gorm/dal/generate" \
        -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
        -H 'sec-ch-ua-mobile: ?0' \
        -H "ec-username: $username" \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36' \
        -H 'Accept: application/json, text/plain, */*' \
        -H "Content-Type: application/json" \
        --data-raw "$request_body")

    # 检查响应
    if [ -z "$response" ]; then
        log_error "Empty response from DAL generation API"
        return 1
    fi

    # 先显示原始响应用于调试
    log_info "API Response: $response"

    # 检查响应是否是有效的 JSON
    if ! echo "$response" | jq . >/dev/null 2>&1; then
        log_error "Invalid JSON response from API: $response"
        return 1
    fi

    # 检查是否成功
    local success=$(echo "$response" | jq -r '.success // false')
    if [ "$success" != "true" ]; then
        local message=$(echo "$response" | jq -r '.message // "Unknown error"')
        log_error "DAL generation failed: $message"
        return 1
    fi

    log_success "DAL generation completed successfully"
}

# 从 GitLab 下载 DAL
download_dal() {
    local sites="$1"
    log_info "Downloading DAL from GitLab for sites: $sites"

    # 创建临时目录
    local temp_dir="/tmp/dal-download-$$"

    # 清理现有的 DAL 目录
    if [ -d "$DAL_DIR" ]; then
        log_warning "Removing existing DAL directory: $DAL_DIR"
        rm -rf "$DAL_DIR"
    fi

    # 克隆仓库到临时目录
    log_info "Cloning DAL repository to temporary directory..."
    if git clone "$GITLAB_REPO_URL" "$temp_dir"; then
        log_success "DAL repository cloned successfully"
    else
        log_error "Failed to clone DAL repository"
        return 1
    fi

    # 创建目标 DAL 目录
    mkdir -p "$DAL_DIR"

    # 提取每个站点的 dal 目录内容到根 dal 目录
    log_info "Extracting dal directory contents (skipping site directories)..."
    IFS=',' read -ra SITE_ARRAY <<< "$sites"
    for site in "${SITE_ARRAY[@]}"; do
        site=$(echo "$site" | xargs) # 去除空格
        local site_dal_dir="$temp_dir/$site/dal"

        if [ -d "$site_dal_dir" ]; then
            log_info "Extracting dal content from $site..."
            # 复制 dal 目录下的所有内容到目标目录
            cp -r "$site_dal_dir"/* "$DAL_DIR/" 2>/dev/null || true
        else
            log_warning "No dal directory found for site: $site"
        fi
    done

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示下载的内容
    log_info "Final DAL structure:"
    if command -v tree &> /dev/null; then
        tree "$DAL_DIR" -L 3
    else
        find "$DAL_DIR" -type d -maxdepth 3 | head -20
    fi

    log_success "DAL downloaded and extracted to: $DAL_DIR"
}

# 显示帮助信息
show_help() {
    echo "DAL Generation and Download Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [SITES]"
    echo ""
    echo "Options:"
    echo "  --dry-run         Show what would be done without executing"
    echo "  --help            Show this help message"
    echo ""
    echo "Arguments:"
    echo "  SITES             Comma-separated list of sites to generate DAL for"
    echo "                    Default: $DEFAULT_SITES"
    echo "                    Examples: ops"
    echo "                              ops,admin"
    echo "                              fulfillment,membership"
    echo ""
    echo "This script will:"
    echo "  1. Login to ec-ops system"
    echo "  2. Call DAL generation API for specified sites"
    echo "  3. Download generated DAL from GitLab"
    echo "  4. Extract only the dal/ directory content (skip site directories)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Generate all sites"
    echo "  $0 ops                       # Generate only ops site"
    echo "  $0 ops,admin                 # Generate ops and admin sites"
    echo "  $0 --dry-run fulfillment     # Test mode for fulfillment site"
    echo ""
}

# 主函数
main() {
    # 处理命令行参数
    local dry_run=false
    local sites="$DEFAULT_SITES"

    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            --*)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                # 第一个非选项参数作为 sites
                sites="$1"
                shift
                ;;
        esac
    done

    log_info "Starting DAL generation and download process..."
    log_info "Project root: $PROJECT_ROOT"
    log_info "Target sites: $sites"

    if [ "$dry_run" = true ]; then
        log_warning "DRY RUN MODE - No actual operations will be performed"
    fi

    # 检查依赖
    check_dependencies
    
    # 登录
    local credentials
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would login to: $API_BASE_URL"
        log_info "[DRY RUN] Would use username: $USERNAME"
        log_info "[DRY RUN] Would set headers: ec-user-id, ec-username"
        credentials="dummy-token|dummy-user-id|dummy-username"
        log_success "[DRY RUN] Authentication credentials obtained"
    else
        credentials=$(login)
        if [ $? -eq 0 ] && [ -n "$credentials" ]; then
            local token=$(printf "%s" "$credentials" | cut -d'|' -f1)
            local user_id=$(printf "%s" "$credentials" | cut -d'|' -f2)
            local username=$(printf "%s" "$credentials" | cut -d'|' -f3)
            log_success "Authentication credentials obtained (user: $username, id: $user_id)"
        else
            log_error "Login failed"
            exit 1
        fi
    fi

    # 生成 DAL
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would call DAL generation API: $API_BASE_URL/api/ops/gorm/dal/generate"
        log_info "[DRY RUN] Would include headers: Authorization, ec-user-id, ec-username"
        log_info "[DRY RUN] Would generate for sites: $sites"
        log_success "[DRY RUN] DAL generation completed"
    else
        if generate_dal "$credentials" "$sites"; then
            log_success "DAL generation completed"
        else
            log_error "DAL generation failed"
            exit 1
        fi
    fi

    # 等待一段时间让生成完成
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would wait 10 seconds for generation to complete"
    else
        log_info "Waiting 10 seconds for generation to complete..."
        sleep 10
    fi

    # 下载 DAL
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would clone repository: $GITLAB_REPO_URL"
        log_info "[DRY RUN] Would extract dal content from sites: $sites"
        log_info "[DRY RUN] Would download to: $DAL_DIR"
        log_success "[DRY RUN] DAL download completed"
    else
        if download_dal "$sites"; then
            log_success "DAL download completed"
        else
            log_error "DAL download failed"
            exit 1
        fi
    fi
    
    log_success "🎉 All tasks completed successfully!"
    log_info "DAL files are now available in: $DAL_DIR"
}

# 执行主函数
main "$@"
