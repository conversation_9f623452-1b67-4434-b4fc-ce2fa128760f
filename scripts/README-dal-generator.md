# DAL 生成和下载脚本

## 概述

`generate-and-download-dal.sh` 是一个自动化脚本，用于：
1. 登录到 ec-ops 系统
2. 调用 API 生成 DAL 代码
3. 从 GitLab 下载生成的 DAL 到项目根目录

## 功能特性

- 🔐 **自动登录**: 参考 `sync-influencer-videos-from-feishu.mjs` 的 login 函数实现
- 🚀 **API 调用**: 调用 DAL 生成 API，包含必要的用户身份头部
- 📥 **自动下载**: 从 GitLab 仓库下载最新的 DAL 代码
- 🎨 **彩色输出**: 清晰的日志输出，便于跟踪进度
- ✅ **错误处理**: 完善的错误检查和处理机制
- 🧪 **测试模式**: --dry-run 选项用于测试而不执行实际操作

## 依赖要求

脚本运行前会自动检查以下依赖：

- `curl` - 用于 HTTP 请求
- `jq` - 用于 JSON 解析
- `git` - 用于克隆仓库

### 安装依赖

**macOS:**
```bash
brew install jq
```

**Ubuntu/Debian:**
```bash
sudo apt-get install jq curl git
```

**CentOS/RHEL:**
```bash
sudo yum install jq curl git
```

## 使用方法

### 基本使用

```bash
# 在项目根目录执行
./scripts/generate-and-download-dal.sh
```

### 执行流程

1. **依赖检查**: 验证所需工具是否安装
2. **登录认证**: 使用预配置的凭据登录 ec-ops
3. **触发生成**: 调用 DAL 生成 API
4. **等待完成**: 等待 10 秒确保生成完成
5. **下载代码**: 从 GitLab 克隆最新的 DAL 代码
6. **清理整理**: 移除 .git 目录，显示目录结构

## 配置说明

脚本中的主要配置项：

```bash
API_BASE_URL="https://ec-ops.blueorigin.cn"
USERNAME="<EMAIL>"
PASSWORD="BO2024$+2"
GITLAB_REPO_URL="https://git.blueorigin.work/e-commerce/gorm-generated-dal.git"
```

## 技术实现

### 请求头设置

脚本参考 `sync-influencer-videos-from-feishu.mjs` 的实现，在调用 DAL 生成 API 时包含必要的请求头：

```bash
# 登录响应包含用户信息
{
  "token": "Bearer xxx",
  "id": "user_id",
  "name": "username"
}

# API 调用时设置的请求头
Authorization: Bearer xxx
ec-user-id: user_id
ec-username: username
Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7
Content-Type: application/json
```

这些请求头对于 API 的正确执行是必需的，确保了用户身份的正确传递。

## 输出结构

执行成功后，DAL 代码将下载到项目根目录的 `dal/` 文件夹中：

```
dal/
├── ops/
│   ├── dal/
│   │   ├── model/
│   │   └── query/
│   └── *.enhanced.go
├── admin/
│   ├── dal/
│   │   ├── model/
│   │   └── query/
│   └── *.enhanced.go
├── fulfillment/
└── membership/
```

## 日志输出

脚本提供彩色的日志输出：

- 🔵 **[INFO]** - 一般信息
- 🟢 **[SUCCESS]** - 成功操作
- 🟡 **[WARNING]** - 警告信息
- 🔴 **[ERROR]** - 错误信息

## 错误处理

脚本包含完善的错误处理：

- **依赖检查失败**: 提示安装缺失的工具
- **登录失败**: 显示详细的错误信息
- **API 调用失败**: 显示 API 响应错误
- **Git 克隆失败**: 提示网络或权限问题

## 故障排除

### 常见问题

1. **jq 命令未找到**
   ```bash
   brew install jq  # macOS
   sudo apt-get install jq  # Ubuntu
   ```

2. **登录失败**
   - 检查网络连接
   - 验证用户名和密码是否正确
   - 确认 API 服务是否正常

3. **Git 克隆失败**
   - 检查网络连接
   - 验证 GitLab 访问权限
   - 确认仓库 URL 是否正确

4. **权限问题**
   ```bash
   chmod +x scripts/generate-and-download-dal.sh
   ```

## 示例输出

```bash
$ ./scripts/generate-and-download-dal.sh
[INFO] Starting DAL generation and download process...
[INFO] Project root: /Users/<USER>/work/gitlab/ec-ops
[INFO] Checking dependencies...
[SUCCESS] All dependencies are available
[INFO] Logging in to ec-ops...
[SUCCESS] Login successful
[SUCCESS] Authentication token obtained
[INFO] Calling DAL generation API...
[SUCCESS] DAL generation completed successfully
[SUCCESS] DAL generation completed
[INFO] Waiting 10 seconds for generation to complete...
[INFO] Downloading DAL from GitLab...
[INFO] Cloning DAL repository...
[SUCCESS] DAL repository cloned successfully
[INFO] Removing .git directory...
[INFO] Downloaded DAL structure:
dal/
├── ops/
├── admin/
├── fulfillment/
└── membership/
[SUCCESS] DAL downloaded to: /Users/<USER>/work/gitlab/ec-ops/dal
[SUCCESS] 🎉 All tasks completed successfully!
[INFO] DAL files are now available in: /Users/<USER>/work/gitlab/ec-ops/dal
```

## 注意事项

- 脚本会删除现有的 `dal/` 目录，请确保没有重要的本地修改
- 生成过程可能需要几分钟时间，请耐心等待
- 确保网络连接稳定，避免下载中断
- 建议在项目根目录执行脚本
