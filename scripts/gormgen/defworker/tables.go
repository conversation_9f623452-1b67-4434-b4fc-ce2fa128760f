package defworker

import (
	"fmt"
	"gorm.io/gorm"
)

func GetTables(db *gorm.DB) []string {
	tableQuery := `SELECT table_name FROM information_schema.tables 
						WHERE table_schema = 'bo_service_ec' AND table_name NOT LIKE '%.%';`

	var tables []string
	if err := db.Debug().Raw(tableQuery).Scan(&tables).Error; err != nil {
		fmt.Println(`db query table names error:`, err)
		return []string{}
	}

	return tables
}
