#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONVERT_SCRIPT="$SCRIPT_DIR/convert/convert_query.go"

# Convert snake_case to camelCase
to_camel_case() {
    local input=$1
    local base_name=$(basename "$input")
    local dir_name=$(dirname "$input")
    
    # Split the filename and extension
    local name_part=$(echo "$base_name" | sed 's/\.[^.]*$//')
    local ext_part=$(echo "$base_name" | grep -o '\.[^.]*$' || echo '')
    
    # Convert to camelCase:
    # 1. First letter of each word after underscore to uppercase
    # 2. Remove all underscores
    # Example: cms_post_category -> cmsPostCategory
    local camel_name=$(echo "$name_part" | awk 'BEGIN{FS="_";OFS=""} {
        for(i=1;i<=NF;i++){
            if(i==1){
                $i=tolower($i)
            } else {
                $i=toupper(substr($i,1,1)) tolower(substr($i,2))
            }
        }
        print
    }')
    
    # If directory is ".", just return the filename
    if [ "$dir_name" = "." ]; then
        echo "${camel_name}${ext_part}"
    else
        echo "${dir_name}/${camel_name}${ext_part}"
    fi
}

# Clean generated temporary files
cleanup_temp_files() {
    local project_name=$1
    local temp_dir="$SCRIPT_DIR/output/$project_name"
    if [ -d "$temp_dir" ]; then
        echo "Cleaning up temporary files in $temp_dir..."
        rm -rf "$temp_dir"
        echo "Cleanup completed."
    fi
}

# Fix import paths in generated files
fix_import_paths() {
    local file=$1
    local project_name=$2
    local temp_file="${file}.tmp"
    
    # Replace import paths
    sed "s|gormgen/output/${project_name}/dal/model|${project_name}/dal/model|g" "$file" > "$temp_file"
    mv "$temp_file" "$file"
}

# Copy files to project
copy_files_to_project() {
    local project_name=$1
    local project_dir="$ROOT_DIR/$project_name"
    local SOURCE_DIR="$SCRIPT_DIR/output/$project_name/dal"
    local DEST_DIR="$project_dir/dal"
    local OVERWRITE_PATTERNS=("*.gen.go" "gen.go")

    if [ ! -d "$SOURCE_DIR" ]; then
        echo "Error: Source directory $SOURCE_DIR does not exist"
        exit 1
    fi

    mkdir -p "$DEST_DIR"

    # Create directory structure
    find "$SOURCE_DIR" -type d | while read -r dir; do
        rel_path="${dir#$SOURCE_DIR}"
        dest_subdir="${DEST_DIR}${rel_path}"
        mkdir -p "$dest_subdir"
    done

    # Process and copy files
    find "$SOURCE_DIR" -type f | while read -r file; do
        rel_path="${file#$SOURCE_DIR}"
        
        # Check if file ends with .enhanced.go
        if [[ "$file" == *.enhanced.go ]]; then
            # For .enhanced.go files, use original filename without any conversion
            dest_file="${DEST_DIR}${rel_path}"

            # Check if destination file exists
            if [ -f "$dest_file" ]; then
                echo "skip[.enhanced.go exists]: $file -> $dest_file"
                continue  # Skip copying if file exists
            else
                echo "copy[.enhanced.go]: $file -> $dest_file"
            fi
        else
            # Convert other filenames to camelCase
            camel_rel_path=$(to_camel_case "$rel_path")
            dest_file="${DEST_DIR}${camel_rel_path}"
        fi

        filename=$(basename "$file")
        should_overwrite=false
        for pattern in "${OVERWRITE_PATTERNS[@]}"; do
            case "$filename" in $pattern)
                should_overwrite=true
                break
                ;;
            esac
        done

        if [ "$should_overwrite" = true ] || [ ! -f "$dest_file" ]; then
            # Create parent directory if it doesn't exist
            mkdir -p "$(dirname "$dest_file")"

            # First fix import paths in the source file
            fix_import_paths "$file" "$project_name"

            # Then copy the fixed file
            cp -f "$file" "$dest_file"
            echo "copy[overwrite]: $file -> $dest_file"
        fi
    done

    echo "New *.gen.go files have been copied with camelCase filenames..."
}

# Check if project name is provided
if [[ -z "$1" ]]; then
    echo "Error: Project name is required"
    echo "Usage: $0 <project-name>"
    echo "Example: $0 cms"
    exit 1
fi

project_name=$1

# Change to generator directory
cd "$SCRIPT_DIR" || exit 1
echo "Working directory: $(pwd)"

echo "Executing go mod tidy..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "go mod tidy failed"
    exit 1
fi

echo "Executing go run main.go..."
go run ./ -projectName="$project_name"
echo "GORM Gen completed..."

echo "Gorm Gen query Model slice replacing to alias..."
if [ -f "$CONVERT_SCRIPT" ]; then
    go run "$CONVERT_SCRIPT" -dir="output/$project_name/dal/query"
    echo "Gorm Gen query Model slice replace to alias succeeded..."
else
    echo "Warning: Convert script not found at $CONVERT_SCRIPT, skipping..."
fi

# Copy files
copy_files_to_project "$project_name"

# Clean up temporary files
cleanup_temp_files "$project_name"
