# GORM Data Access Logic 生成

基于 `gorm gen` 生成， 开发文档：https://gorm.io/gen/ 。

## GORM Gen 适用场景
- 中大型项目
- 需要长期维护的项目
- 团队协作开发
- 需要严格类型安全的场景
- 有大量重复CRUD操作的场景
<br/>
## `GORM Gen` 与原生 `GORM` 的优势对比
### 类型安全与代码生成(编译期)
```go
// GORM Gen 优势: 生成的类型安全查询
user, err := q.User.Where(q.User.Name.Eq("John")).First()
// 编译时检查字段名和类型，Name拼写错误或类型不匹配会直接报错

// 原生GORM使用字符串字段名
var user User
err := db.Where("name = ?", "John").First(&user).Error
// 字段名拼写错误只能在运行时发现
```

### CRUD 操作比较
```go
// GORM Gen 生成的方法更直观
    err := q.User.Create(&User{Name: "John"})
// 原生 GORM
    err := db.Create(&User{Name: "John"}).Error


// GORM Gen 自动生成的方法
    users, err := q.User.FindByPage(1, 10) // 自动生成的分页方法
    activeUsers, err := q.User.Where(
    q.User.Age.Gt(18),
    q.User.IsActive.Eq(true),
    ).Find()

// 原生 GORM
    var users []User
    var count int64
    db.Model(&User{}).Count(&count)
    err := db.Offset(0).Limit(10).Find(&users).Error

    var activeUsers []User
    err := db.Where("age > ? AND is_active = ?", 18, true).Find(&activeUsers).Error
```

### 性能比较
- GORM Gen 优势:
生成的代码直接调用底层方法，减少反射使用
查询条件构建在代码生成时完成，运行时效率更高

- 原生 GORM:
依赖运行时反射解析模型结构
条件构建需要运行时处理
<br/>

## 模块功能：
### 1、自定义 `templates` 实现了所有表的基础 CRUD 的接口

### 2、生成 model 和 query
```shell
sh gen.sh
```

### 3、拷贝 model 和 query 到指定项目中的路径

```shell
# cp model cms/repository/
sh copy.sh cms
```

### 4、编译报错修复
- 拷贝后利用 Goland `SHIFT+COMMAND+R` 替换`query` 包中错误的 `model`引入路径

### 5、清理 gormgen 中的 output
```shell
sh clean.sh
```
 
<br/>

## GORM Gen 用法部分示例
```go

u := query.User

// Get the first record ordered by primary key
user, err := u.WithContext(ctx).First()
// SELECT * FROM users ORDER BY id LIMIT 1;

users, err := u.WithContext(ctx).Where(u.Name.Eq("modi"), u.Age.Gte(17)).Find()
// SELECT * FROM users WHERE name = 'modi' AND age >= 17;

// Time
users, err := u.WithContext(ctx).Where(u.Birthday.Gt(birthTime).Find()
// SELECT * FROM users WHERE birthday > '2000-01-01 00:00:00';

u.WithContext(ctx).Where(u.Activate.Is(true)).UpdateSimple(u.Age.Value(17), u.Number.Zero(), u.Birthday.Null())
// UPDATE users SET age=17, number=0, birthday=NULL, updated_at='2013-11-17 21:34:10' WHERE active=true;

type Result struct {
    Name  string
    Email string
    ID    int64
}

var result Result

err := u.WithContext(ctx).Select(u.Name, e.Email).LeftJoin(e, e.UserID.EqCol(u.ID)).Scan(&result)
// SELECT users.name, emails.email FROM `users` left join emails on emails.user_id = users.id

```