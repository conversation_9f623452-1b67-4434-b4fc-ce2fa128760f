package main

import (
	"bytes"
	"flag"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"io/fs"
	"log"
	"os"
	"path/filepath"
	"strings"
)

func main() {
	// 解析命令行参数
	dir := flag.String("dir", ".", "directory to process")
	dryRun := flag.Bool("dry-run", false, "only show changes without modifying files")
	flag.Parse()

	// 遍历目录处理 Go 文件
	err := filepath.WalkDir(*dir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 只处理 .go 文件，忽略测试文件
		if !d.IsDir() && strings.HasSuffix(path, ".go") && !strings.HasSuffix(path, "_test.go") {
			processFile(path, *dryRun)
		}
		return nil
	})

	if err != nil {
		log.Fatalf("Error walking directory: %v", err)
	}
}

func processFile(filename string, dryRun bool) {
	{
		fset := token.NewFileSet()
		file, err := parser.ParseFile(fset, filename, nil, parser.ParseComments)
		if err != nil {
			log.Printf("Parse error in %s: %v", filename, err)
			return
		}

		modified := false
		ast.Inspect(file, func(n ast.Node) bool {
			// 处理所有可能包含类型的节点
			switch x := n.(type) {
			case *ast.Field, *ast.ValueSpec, *ast.TypeAssertExpr, *ast.CompositeLit, *ast.CallExpr:
				processNode(fset, n, &modified)
			case *ast.FuncType:
				processFuncType(fset, x, &modified)
			}
			return true
		})

		if !modified {
			return
		}

		if err := fixFindMethodsWithSliceAlias(file); err != nil {
			log.Printf("fix Find methods with slice alias error: %v", err)
			return
		}

		fmt.Printf("Processing: %s\n", filename)
		var buf bytes.Buffer
		if err := format.Node(&buf, fset, file); err != nil {
			log.Printf("Format error: %v", err)
			return
		}

		if dryRun {
			fmt.Println(buf.String())
			return
		}

		if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
			log.Printf("Write error: %v", err)
		}
	}
}

// 处理通用节点
func processNode(fset *token.FileSet, node ast.Node, modified *bool) {
	var typeExpr ast.Expr
	switch n := node.(type) {
	case *ast.Field:
		typeExpr = n.Type
	case *ast.ValueSpec:
		typeExpr = n.Type
	case *ast.TypeAssertExpr:
		typeExpr = n.Type
	case *ast.CompositeLit:
		typeExpr = n.Type
	case *ast.CallExpr:
		typeExpr = n.Fun
	default:
		return
	}

	if newType := replaceType(fset, typeExpr); newType != nil {
		switch n := node.(type) {
		case *ast.Field:
			n.Type = newType
		case *ast.ValueSpec:
			n.Type = newType
		case *ast.TypeAssertExpr:
			n.Type = newType
		case *ast.CompositeLit:
			n.Type = newType
		case *ast.CallExpr:
			n.Fun = newType
		}
		*modified = true
	}
}

// 处理函数类型
func processFuncType(fset *token.FileSet, funcType *ast.FuncType, modified *bool) {
	// 处理参数
	if funcType.Params != nil {
		for _, field := range funcType.Params.List {
			if newType := replaceType(fset, field.Type); newType != nil {
				field.Type = newType
				*modified = true
			}
		}
	}
	// 处理返回值
	if funcType.Results != nil {
		for _, field := range funcType.Results.List {
			if newType := replaceType(fset, field.Type); newType != nil {
				field.Type = newType
				*modified = true
			}
		}
	}
}

// 核心类型替换逻辑
func replaceType(fset *token.FileSet, typeExpr ast.Expr) ast.Expr {
	switch t := typeExpr.(type) {
	case *ast.StarExpr: // 处理 *[]*model.XXX 情况
		if arrayType, ok := t.X.(*ast.ArrayType); ok {
			if newType := checkAndReplaceModelArray(fset, arrayType); newType != nil {
				return &ast.StarExpr{
					Star: t.Star,
					X:    newType,
				}
			}
		}
	case *ast.ArrayType: // 处理 []*model.XXX 情况
		return checkAndReplaceModelArray(fset, t)
	}
	return nil
}

// 检查并替换 model 相关的数组类型
func checkAndReplaceModelArray(fset *token.FileSet, arrayType *ast.ArrayType) ast.Expr {
	// 处理 *model.XXX
	starExpr, ok := arrayType.Elt.(*ast.StarExpr)
	if !ok {
		return nil
	}

	// 处理 model.XXX
	selExpr, ok := starExpr.X.(*ast.SelectorExpr)
	if !ok {
		return nil
	}

	// 确认是 model 包
	pkgIdent, ok := selExpr.X.(*ast.Ident)
	if !ok || pkgIdent.Name != "model" {
		return nil
	}

	// 构造新的类型 model.XXXSlice
	return &ast.SelectorExpr{
		X:   &ast.Ident{Name: "model", NamePos: pkgIdent.NamePos},
		Sel: &ast.Ident{Name: selExpr.Sel.Name + "Slice", NamePos: selExpr.Sel.NamePos},
	}
}

func fixFindMethodsWithSliceAlias(file *ast.File) error {
	needsFmtImport := false
	ast.Inspect(file, func(n ast.Node) bool {
		fn, ok := n.(*ast.FuncDecl)
		if !ok {
			return true
		}

		// 只处理Find方法
		if fn.Name.Name != "Find" {
			return true
		}

		// 检查返回类型是否是(model.XSlice, error)
		if len(fn.Type.Results.List) != 2 {
			return true
		}

		returnType, ok := fn.Type.Results.List[0].Type.(*ast.SelectorExpr)
		if !ok || !strings.HasSuffix(returnType.Sel.Name, "Slice") {
			return true
		}

		// 提取模型信息
		modelPkg := returnType.X.(*ast.Ident).Name
		baseType := strings.TrimSuffix(returnType.Sel.Name, "Slice")

		// 构建新的函数体
		newBody := []ast.Stmt{
			// result, err := c.DO.Find()
			&ast.AssignStmt{
				Lhs: []ast.Expr{ast.NewIdent("result"), ast.NewIdent("err")},
				Tok: token.DEFINE,
				Rhs: []ast.Expr{
					&ast.CallExpr{
						Fun: &ast.SelectorExpr{
							X:   &ast.SelectorExpr{X: ast.NewIdent(strings.ToLower(baseType[0:1])), Sel: ast.NewIdent("DO")},
							Sel: ast.NewIdent("Find"),
						},
					},
				},
			},
			// if err != nil { return nil, err }
			&ast.IfStmt{
				Cond: &ast.BinaryExpr{X: ast.NewIdent("err"), Op: token.NEQ, Y: ast.NewIdent("nil")},
				Body: &ast.BlockStmt{
					List: []ast.Stmt{
						&ast.ReturnStmt{
							Results: []ast.Expr{
								&ast.CompositeLit{
									Type: &ast.SelectorExpr{
										X:   ast.NewIdent(modelPkg),
										Sel: ast.NewIdent(baseType + "Slice"),
									},
								},
								ast.NewIdent("err"),
							},
						},
					},
				},
			},
			// if slice, ok := result.([]*model.X); ok {
			&ast.IfStmt{
				Init: &ast.AssignStmt{
					Lhs: []ast.Expr{ast.NewIdent("slice"), ast.NewIdent("ok")},
					Tok: token.DEFINE,
					Rhs: []ast.Expr{
						&ast.TypeAssertExpr{
							X: ast.NewIdent("result"),
							Type: &ast.ArrayType{
								Elt: &ast.StarExpr{
									X: &ast.SelectorExpr{
										X:   ast.NewIdent(modelPkg),
										Sel: ast.NewIdent(baseType),
									},
								},
							},
						},
					},
				},
				Cond: ast.NewIdent("ok"),
				Body: &ast.BlockStmt{
					List: []ast.Stmt{
						// return model.XSlice(slice), err
						&ast.ReturnStmt{
							Results: []ast.Expr{
								&ast.CallExpr{
									Fun: &ast.SelectorExpr{
										X:   ast.NewIdent(modelPkg),
										Sel: ast.NewIdent(baseType + "Slice"),
									},
									Args: []ast.Expr{ast.NewIdent("slice")},
								},
								ast.NewIdent("err"),
							},
						},
					},
				},
			},
			// return nil, err
			&ast.ReturnStmt{
				Results: []ast.Expr{
					&ast.CompositeLit{
						Type: &ast.SelectorExpr{
							X:   ast.NewIdent(modelPkg),
							Sel: ast.NewIdent(baseType + "Slice"),
						},
					},
					ast.NewIdent("err"),
				},
			},
		}

		fn.Body.List = newBody
		needsFmtImport = true
		return true
	})

	if needsFmtImport {
		addFmtImport(file)
	}
	return nil
}

func addFmtImport(file *ast.File) {
	for _, imp := range file.Imports {
		if imp.Path.Value == `"fmt"` {
			return
		}
	}

	file.Imports = append(file.Imports, &ast.ImportSpec{
		Path: &ast.BasicLit{
			Kind:  token.STRING,
			Value: `"fmt"`,
		},
	})

	// 确保有import声明
	hasImportDecl := false
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.IMPORT {
			hasImportDecl = true
			break
		}
	}

	if !hasImportDecl {
		importDecl := &ast.GenDecl{
			Tok:   token.IMPORT,
			Specs: []ast.Spec{file.Imports[len(file.Imports)-1]},
		}
		file.Decls = append([]ast.Decl{importDecl}, file.Decls...)
	}
}
