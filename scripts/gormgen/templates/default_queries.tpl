{{define "query_extend"}}
package {{.Package}}

import (
    "context"
    "gorm.io/gen"
    "{{.ModelPkgPath}}"
)

// FindById 根据主键查询单条记录
func (c {{.ModelName | toSmallCamelCase}}) FindById(ctx context.Context, id int64) (*model.{{.ModelName}}, error) {
    return c.WithContext(ctx).Where(c.ID.Eq(id)).First()
}

// FindByIds 根据多个主键查询记录
func (c {{.ModelName | toSmallCamelCase}}) FindByIds(ctx context.Context, ids []int64) (model.{{.ModelName}}List, error) {
    return c.WithContext(ctx).Where(c.ID.In(ids...)).Find()
}

// FindByConditions 根据条件查询记录
func (c {{.ModelName | toSmallCamelCase}}) FindByConditions(ctx context.Context, cond []gen.Condition) (model.{{.ModelName}}List, error) {
    return c.WithContext(ctx).Where(cond...).Find()
}

// DeleteById 根据主键删除记录
func (c {{.ModelName | toSmallCamelCase}}) DeleteById(ctx context.Context, id int64) error {
    _, err := c.WithContext(ctx).Where(c.ID.Eq(id)).Delete()
    return err
}

// DeleteByConditions 根据条件删除记录
func (c {{.ModelName | toSmallCamelCase}}) DeleteByConditions(ctx context.Context, cond []gen.Condition) (int64, error) {
    ret, err := c.WithContext(ctx).Where(cond...).Delete()
    return ret.RowsAffected, err
}

// UpdateById 根据主键更新记录
func (c {{.ModelName | toSmallCamelCase}}) UpdateById(ctx context.Context, id int64, values map[string]interface{}) error {
    _, err := c.WithContext(ctx).Where(c.ID.Eq(id)).Updates(values)
        return err
}

// UpdateByConditions 根据条件更新记录
func (c {{.ModelName | toSmallCamelCase}}) UpdateByConditions(ctx context.Context, id int64, cond []gen.Condition, values map[string]interface{}) error {
    _, err := c.WithContext(ctx).Where(cond...).Updates(values)
        return err
}


{{end}}