package main

import (
	"bytes"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

type modelInfo struct {
	ModelName      string
	PackageName    string
	QueryInterface string
}

func genEnhancedQuery() {
	err := generateDalFile("./templates/gen_dal.tpl", `output/`+projectName+`/dal/dal.gen.go`)
	if err != nil {
		println("dal.go 文件生成失败！", err.Error())
		panic(err)
	}
	println("dal.go 文件生成成功！")

	// 解析生成的 query 文件
	filenames, _ := findGenQueryGoFiles(`output/` + projectName + `/dal/query/`)
	for _, filename := range filenames {
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, filename, nil, parser.ParseComments)
		if err != nil {
			panic(err)
		}

		// 收集模型信息
		var info modelInfo
		// 从AST中提取信息
		ast.Inspect(node, func(n ast.Node) bool {
			switch x := n.(type) {
			case *ast.TypeSpec:
				if strings.HasSuffix(x.Name.Name, "Do") {
					info.QueryInterface = x.Name.Name
				}
			case *ast.File:
				info.PackageName = x.Name.Name
			}
			return true
		})

		info.ModelName = strings.TrimSuffix(info.QueryInterface[1:], "Do")

		// 生成扩展文件
		generateExtensionFile(info)
	}
}

func generateExtensionFile(info modelInfo) {
	// 使用模板生成代码
	// 1. 读取模板文件内容
	tplPath := "templates/gen_enhanced.tpl"
	tplData, err := os.ReadFile(tplPath)
	if err != nil {
		return
	}

	// 2. 解析模板
	var buf bytes.Buffer
	t, err := template.New("gen_enhanced").Parse(fmt.Sprintf(string(tplData), projectName))
	if t == nil {
		panic(err)
	}
	// 3. 执行模板渲染
	if err = t.Execute(&buf, info); err != nil {
		panic(err)
	}
	// 格式化代码
	formatted, err := format.Source(buf.Bytes())
	if err != nil {
		panic(err)
	}

	// 写入文件
	toSmallCamel := strings.ToLower(string(info.ModelName[0])) + info.ModelName[1:]
	outputPath := filepath.Join("output/"+projectName+"/dal", toSmallCamel+".enhanced.go")
	if ok, _ := PathExists(outputPath); ok {
		return
	}
	if err := ioutil.WriteFile(outputPath, formatted, 0644); err != nil {
		panic(err)
	}

	fmt.Printf("Generated extension file: %s\n", outputPath)
}

// findGenQueryGoFiles 递归查找目录下所有 *.gen.go 文件
func findGenQueryGoFiles(dir string) ([]string, error) {
	var genFiles []string

	// 使用 filepath.Walk 递归遍历目录
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件是否以 .gen.go 结尾
		if strings.HasSuffix(path, ".gen.go") {
			genFiles = append(genFiles, path)
		}

		return nil
	})

	return genFiles, err
}

// generateDalFile 根据 tpl 模板生成 dal.go 文件
func generateDalFile(tplPath, outputPath string) error {
	// 1. 读取模板文件内容
	tplData, err := os.ReadFile(tplPath)
	if err != nil {
		return err
	}

	// 2. 解析模板
	tpl, err := template.New("gen_dal").Parse(string(tplData))
	if err != nil {
		return err
	}

	// 3. 执行模板渲染（这里不需要数据，使用空 map）
	var buf bytes.Buffer
	err = tpl.ExecuteTemplate(&buf, "gen_dal", map[string]interface{}{
		"ProjectName": projectName,
	})
	if err != nil {
		return err
	}

	// 4. 创建输出目录（如果不存在）
	outputDir := outputPath[:strings.LastIndex(outputPath, "/")]
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			return err
		}
	}

	// 5. 写入渲染结果到文件
	err = os.WriteFile(outputPath, buf.Bytes(), 0644)
	if err != nil {
		return err
	}

	return nil
}
