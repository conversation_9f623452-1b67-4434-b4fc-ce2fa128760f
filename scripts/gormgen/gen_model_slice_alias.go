package main

import (
	"fmt"
	"go/types"
	"golang.org/x/tools/go/packages"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"unicode"
)

func generateSliceAliases(modelDir string) {

	var tpl = fmt.Sprintf(`
package %s
type {{.ModelName}}Slice []*{{.ModelName}}
`, filepath.Base(modelDir))

	template.New(``).ParseFiles()
	t, err := template.New(`model_slice`).Parse(tpl)
	if err != nil {
		panic(err)
	}

	// 使用go/packages分析模型包
	cfg := &packages.Config{
		Mode: packages.NeedTypes | packages.NeedTypesInfo,
		Dir:  modelDir,
	}
	pkgs, err := packages.Load(cfg, `.`)
	if err != nil {
		panic(err)
	}

	for _, pkg := range pkgs {
		scope := pkg.Types.Scope()
		for _, name := range scope.Names() {
			obj := scope.Lookup(name)
			if _, ok := obj.Type().Underlying().(*types.Struct); ok {
				path := filepath.Join(modelDir, `slice_`+ToSnakeCase(name)+".go")
				// 如果文件已经存在，按需决定是否覆盖创建，默认不覆盖
				if exists, _ := PathExists(path); exists {
					continue
				}
				// 创建切片别名文件
				f, err := os.Create(path)
				if err != nil {
					panic(err)
				}

				err = t.Execute(f, map[string]interface{}{
					"ModelName": name,
				})
				if err != nil {
					panic(err)
				}
				_ = f.Close()
			}
		}
	}
}

// PathExists 检查路径是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err // 其他类型的错误
}

// ToSnakeCase 将字符串转换为下划线命名
func ToSnakeCase(str string) string {
	var result []rune
	var prevChar rune

	for i, char := range str {
		// 处理非字母数字字符（转换为下划线）
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) {
			if len(result) > 0 && result[len(result)-1] != '_' {
				result = append(result, '_')
			}
			prevChar = char
			continue
		}

		// 当前字符是大写字母
		if unicode.IsUpper(char) {
			// 在前一个大写字母后或数字后插入下划线（处理驼峰和帕斯卡）
			if i > 0 && (unicode.IsLower(prevChar) || (i > 0 && unicode.IsDigit(prevChar))) {
				result = append(result, '_')
			}
			result = append(result, unicode.ToLower(char))
			prevChar = char
			continue
		}

		// 当前是数字，前一个是字母
		if unicode.IsDigit(char) && i > 0 && unicode.IsLetter(prevChar) {
			result = append(result, '_')
		}

		result = append(result, char)
		prevChar = char
	}

	snakeStr := string(result)

	// 清理可能的多余下划线
	snakeStr = strings.ReplaceAll(snakeStr, "__", "_")
	snakeStr = strings.Trim(snakeStr, "_")

	return strings.ToLower(snakeStr)
}
