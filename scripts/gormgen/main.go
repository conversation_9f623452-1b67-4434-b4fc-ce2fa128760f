package main

import (
	"flag"
	"fmt"
	"gormgen/defworker"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// 修改以生成所需表的 Model
var (
	projectName string
	dbUserName  = `ec_test`
	dbPassword  = `ly5Vtm66zH4YamW`
	dbHost      = `rm-wz9u28bqy85v082837o.mysql.rds.aliyuncs.com:3306`
	dbName      = `bo_service_ec`
	dsn         = fmt.Sprintf(`%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local`,
		dbUserName, dbPassword, dbHost, dbName)
)

var (
	projectNamePtr = flag.String("projectName", "cms", "项目名称")
)

func main() {
	flag.Parse()
	projectName = *projectNamePtr
	fmt.Println(` GORM Gen:即将生成 `, projectName, ` 的 dal 代码...`)
	// 连接临时数据库
	g := gen.NewGenerator(gen.Config{
		// 生成完毕后，拷贝到具体服务
		OutPath:      `output/` + projectName + `/dal/query`, // CURD 输出目录 Data Access Layer
		ModelPkgPath: `output/` + projectName + `/dal/model`, // Model 生成路径 Data Access Layer
		//FieldCoverable:   true,                                          // 生成字段覆盖方法
		FieldWithTypeTag: true,
		Mode:             gen.WithDefaultQuery | gen.WithQueryInterface,
	})
	_ = createDirRecursive(g.OutPath)
	_ = createDirRecursive(g.ModelPkgPath)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println(`db connect error:`, err)
	}

	g.UseDB(db)

	var tables []string
	switch projectName {
	case `worker`:
		tables = defworker.GetTables(db)
	}

	for _, t := range tables {
		g.ApplyBasic(g.GenerateModel(t))
	}

	// 为不同 model 自定义 Query 方法
	switch projectName {
	case `worker`:
		defworker.GenInfraDef(g)
	}

	g.Execute()

	// 为所有 Model 生成切片别名
	generateSliceAliases(g.ModelPkgPath)

	// 为所有模型生成自定义查询结构
	genEnhancedQuery()
}

func createDirRecursive(path string) error {
	// 递归创建目录，权限 0755（可读、可写、可执行）
	err := os.MkdirAll(path, 0755)
	if err != nil {
		// 检查是否是因为目录已存在导致的错误
		if !os.IsExist(err) {
			return fmt.Errorf("failed to create directory: %v", err)
		}
	}
	return nil
}
