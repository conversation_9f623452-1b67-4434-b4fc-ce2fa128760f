package common

import "gorm.io/gen"

// QueryByPrimaryKeyId QueryByPrimaryKeyIdWithFlagDeletedFalse 下面两个接口二选一
type QueryByPrimaryKeyId interface {
	// SELECT * FROM @@table WHERE id=@id
	FindById(id int64) (gen.T, error)
}

type QueryByPrimaryKeyIdWithFlagDeletedFalse interface {
	// SELECT * FROM @@table WHERE id=@id AND flag_deleted=0
	FindByIdFlagDeletedFalse(id int64) (gen.T, error)
}

type DeleteByPrimaryKeyId interface {
	// DELETE FROM @@table WHERE id=@id
	DeleteById(id int64) (gen.ResultInfo, error)
}

type FindByIds interface {
	// SELECT * FROM @@table WHERE id IN(@ids)
	FindByIds(ids []int64) ([]*gen.T, error)
}

type FindByConditions interface {
	// SELECT * FROM @@table WHERE @conditions
	FindByConditions(conditions map[string]interface{}) ([]*gen.T, error)
}

type FindByIdsFlagDeletedFalse interface {
	// SELECT * FROM @@table WHERE id IN(@ids) AND flag_deleted=0
	FindByIdsFlagDeletedFalse(ids []int64) ([]*gen.T, error)
}

type DeleteByIds interface {
	// DELETE FROM @@table WHERE id IN(@ids)
	DeleteByIds(ids []int64) (gen.ResultInfo, error)
}

type DeleteByConditions interface {
	// DELETE FROM @@table WHERE @conditions
	DeleteByConditions(conditions map[string]interface{}) (gen.ResultInfo, error)
}
