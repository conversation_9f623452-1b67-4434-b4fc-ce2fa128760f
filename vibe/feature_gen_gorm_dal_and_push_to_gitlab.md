# generate gorm dal and push to gitlab

1. 修改 generateGormDalAndPushToGitlabLogic.go 逻辑参考下面的 go 代码

repo: https://git.blueorigin.work/e-commerce/gorm-generated-dal
目录结构
${database_name}/dal
credential 在 webapi/internal/config/config.go 中 GitLab.Token

2. 生成 dal 的脚本代码需要参考 scripts/gormgen 目录中的代码。而不是下面例子中的代码。但是总体的逻辑是差不多的。

package main

import (
    "fmt"
    "log"
    "os"
    "os/exec"
    "path/filepath"
    "strings"
)

const (
    RepoURL    = "https://git.blueorigin.work/e-commerce/gorm-generated-dal"
    RepoDir    = "/tmp/dal-gen"
    DalBaseDir = "dal"
)

func main() {
    token := os.Getenv("GITLAB_TOKEN")
    if token == "" {
        log.Fatal("GITLAB_TOKEN 环境变量未设置")
    }

    // 1. Clone 或 Pull 仓库
    if err := cloneOrPullRepo(token); err != nil {
        log.Fatal("克隆或更新仓库失败:", err)
    }

    // 2. 生成 GORM DAL 代码（示例：为 db1, db2 生成）
    databases := []string{"users_db", "orders_db", "products_db"}
    for _, db := range databases {
        outputDir := filepath.Join(RepoDir, DalBaseDir, db)
        if err := generateGormDAL(db, outputDir); err != nil {
            log.Printf("生成 %s 失败: %v", db, err)
            continue
        }
        fmt.Printf("✅ 已生成 %s 的 DAL 到 %s\n", db, outputDir)
    }

    // 3. 提交并推送到 main 分支
    if err := gitCommitAndPush(token); err != nil {
        log.Fatal("Git 提交失败:", err)
    }

    fmt.Println("🎉 所有 DAL 代码已成功生成并推送到远程仓库！")
}

// cloneOrPullRepo: 如果目录存在则 git pull，否则 git clone
func cloneOrPullRepo(token string) error {
    if _, err := os.Stat(RepoDir); os.IsNotExist(err) {
        fmt.Println("🔍 仓库不存在，正在克隆...")
        url := fmt.Sprintf(RepoURL, token)
        cmd := exec.Command("git", "clone", url, RepoDir)
        cmd.Stdout = os.Stdout
        cmd.Stderr = os.Stderr
        return cmd.Run()
    }

    fmt.Println("🔄 仓库已存在，正在拉取最新代码...")
    cmd := exec.Command("git", "pull")
    cmd.Dir = RepoDir
    cmd.Stdout = os.Stdout
    cmd.Stderr = os.Stderr
    return cmd.Run()
}

// generateGormDAL: 模拟生成 GORM DAL（替换为你自己的 gen 逻辑）
func generateGormDAL(dbName, outputDir string) error {
    // 创建目录
    if err := os.MkdirAll(outputDir, 0755); err != nil {
        return err
    }

    // 示例：生成一个 dummy 文件
    content := fmt.Sprintf("// Code generated for database: %s\npackage %s\n\ntype User struct{}\n", dbName, dbName)
    return os.WriteFile(filepath.Join(outputDir, "dummy.go"), []byte(content), 0644)

    // 替换为你的 gorm-gen 实际调用逻辑
    // 例如：gen.WithOutFileNamer(...) 等
}

// gitCommitAndPush: 添加、提交、推送
func gitCommitAndPush(token string) error {
    cmds := []*exec.Cmd{
        {Dir: RepoDir, Args: []string{"git", "config", "user.name", "Auto DAL Bot"}},
        {Dir: RepoDir, Args: []string{"git", "config", "user.email", "<EMAIL>"}},
        {Dir: RepoDir, Args: []string{"git", "add", DalBaseDir}},
    }

    for _, cmd := range cmds {
        cmd.Stdout = os.Stdout
        cmd.Stderr = os.Stderr
        if err := cmd.Run(); err != nil {
            return fmt.Errorf("执行 %s 失败: %w", strings.Join(cmd.Args, " "), err)
        }
    }

    // 检查是否有变更
    diffCmd := exec.Command("git", "status", "--porcelain")
    diffCmd.Dir = RepoDir
    diff, _ := diffCmd.Output()
    if len(diff) == 0 {
        fmt.Println("🟢 无代码变更，无需提交")
        return nil
    }

    // 提交
    commitCmd := exec.Command("git", "commit", "-m", fmt.Sprintf("auto: update dal code %s", "2024-01-01"))
    commitCmd.Dir = RepoDir
    commitCmd.Stdout = os.Stdout
    commitCmd.Stderr = os.Stderr
    if err := commitCmd.Run(); err != nil {
        return fmt.Errorf("提交失败: %w", err)
    }

    // 推送（使用 token 认证）
    pushURL := fmt.Sprintf(RepoURL, token)
    setRemoteCmd := exec.Command("git", "remote", "set-url", "origin", pushURL)
    setRemoteCmd.Dir = RepoDir
    if err := setRemoteCmd.Run(); err != nil {
        return fmt.Errorf("设置 remote 失败: %w", err)
    }

    pushCmd := exec.Command("git", "push", "origin", "main")
    pushCmd.Dir = RepoDir
    pushCmd.Stdout = os.Stdout
    pushCmd.Stderr = os.Stderr
    return pushCmd.Run()
}