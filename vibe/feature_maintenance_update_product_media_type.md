# 更新 product_media 的 media_type

1. API 定义在 webapi/desc/fulfillment.api 中 
2. logic 文件位于 webapi/internal/logic/fulfillment_center/updateProductMediaTypeLogic.go

逻辑说明
1. 查询 product_media by id ,不存在则返回错误
2. 如果 product_media 的 media_type 是 scm_white_image 或者 scm_gallery_image 说明已经处理了，无需处理
2. 更新 product_media 的 media_type 如果它是 gallery_image 就更新为 scm_gallery_image ，如果是 white_image 就更新为 scm_white_image
3. 如果 product_media 的 media_hash 是空的，需要帮它计算 hash
4. 返回成功







// CalculateMD5 计算二进制数据的 MD5 哈希值
func CalculateMD5(data []byte) string {
	// 创建 MD5 哈希器
	hasher := md5.New()

	// 写入数据
	hasher.Write(data)

	// 计算哈希值并转换为16进制字符串
	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash)
}