# 为 release plan 创建飞书审批

# Context
API webapi/desc/release.api
post /plans/:plan_id/feishu-approvals (PlanIdReq) returns (BaseResponse)

# 需求

再创建 release plan 系统内的 approval 完成之后，可以调用此 API 创建飞书审批

这个功能可以理解为
1. 系统本身有个 approval 的流程。只有这个流程完成之后，才能创建飞书的 approval 流程。
2. 飞书的 approval 中，只要填充基础的发布信息就好了，其他的 SQL 什么的全部都不要填写。

在 release_plans 表中，增加一个字段 feishu_approval_instance_id 用来记录

2. 可以参考的代码 如下

package larkclient

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.blueorigin.work/e-commerce/locale-hub-common/locale/consts"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"github.com/zeromicro/go-zero/core/logx"
)

var LarkStatus2Int = map[string]int32{
	larkapproval.StatusPending:    1,
	larkapproval.StatusApproved:   2,
	larkapproval.StatusRejected:   3,
	larkapproval.StatusCanceled:   4,
	larkapproval.StatusDeleted:    5,
	larkapproval.StatusHidden:     6,
	larkapproval.StatusTerminated: 7,
}

// SampleBorrowApprovalInstanceReq 创建借样飞书申请审批单
type SampleBorrowApprovalInstanceReq interface {
	Do(ctx context.Context) (instanceId string, err error)
}

type SampleBorrowApprovalInstanceForm struct {
	ID    string      `json:"id"`    // 控件ID
	Type  string      `json:"type"`  // 控件类型（如input、date、attachmentV2等）
	Value interface{} `json:"value"` // 控件值
}

type SampleBorrowApprovalInstanceForms []*SampleBorrowApprovalInstanceForm

type sampleBorrowApprovalInstanceReq struct {
	LarkUserID string
	Forms      SampleBorrowApprovalInstanceForms
}

type SampleBorrowApprovalInstanceReqOption func(*sampleBorrowApprovalInstanceReq)

func SampleBorrowApprovalWithLarkUserID(userId string) SampleBorrowApprovalInstanceReqOption {
	return func(req *sampleBorrowApprovalInstanceReq) {
		req.LarkUserID = userId
	}
}

func SampleBorrowApprovalWithForms(forms SampleBorrowApprovalInstanceForms) SampleBorrowApprovalInstanceReqOption {
	return func(req *sampleBorrowApprovalInstanceReq) {
		req.Forms = forms
	}
}

func NewSampleBorrowApprovalInstanceReq(options ...SampleBorrowApprovalInstanceReqOption) SampleBorrowApprovalInstanceReq {
	req := &sampleBorrowApprovalInstanceReq{}
	for _, option := range options {
		option(req)
	}
	return req
}

// Do 创建样品借样飞书申请
func (r *sampleBorrowApprovalInstanceReq) Do(ctx context.Context) (instanceId string, err error) {
	region := ctx.Value(consts.XLocaleHeaderKey).(string)
	client := GetClientByRegion(region)
	if client == nil {
		err = errors.New(`按 Locale 获取 lark client 失败`)
		logx.Error(err)
		return
	}

	conf := svcCtx.Config.GetSampleReqLarkConf(region)
	formDataBytes, _ := json.Marshal(r.Forms)
	larkReq := larkapproval.NewCreateInstanceReqBuilder().
		InstanceCreate(larkapproval.NewInstanceCreateBuilder().
			ApprovalCode(conf.ApprovalCode). // 替换为审批模板的 approval_code
			UserId(r.LarkUserID).            // 发起人UserID
			Form(string(formDataBytes)).
			Build(),
		).Build()
	// 4. 调用API
	resp, apiErr := client.Approval.Instance.Create(ctx, larkReq)
	if apiErr != nil {
		err = apiErr
		logx.Errorf("创建审批实例失败: %v\n", err)
		return
	}

	// 5. 处理响应
	if !resp.Success() {
		fmt.Printf("API返回错误: %v\n", resp.Msg)
		return
	}
	if resp.Data != nil && resp.Data.InstanceCode != nil {
		instanceId = *resp.Data.InstanceCode
	}
	return
}



package task

import (
	"context"
	"fmt"
	"git.blueorigin.work/e-commerce/locale-hub-common/datasource"
	"git.blueorigin.work/e-commerce/locale-hub-common/locale/consts"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"io"
	"net/http"
	"os"
	"time"
	"worker/api/internal/client/larkclient"
	"worker/api/internal/svc"
	"worker/common/rediscache"
	"worker/dal"
	"worker/dal/model"
	"worker/dal/query"
)

// SyncSampleReq2LarkApproval 同步商品借样申请到飞书审批
func SyncSampleReq2LarkApproval(svcCtx *svc.ServiceContext, region string) {
	ctx := context.WithValue(context.Background(), consts.XLocaleHeaderKey, region)
	borrowReq, err := dal.GetSampleBorrowRequestEnhanced(ctx).GetSomePending()
	if err != nil {
		logx.Error(`GetSampleBorrowRequest error:`, err)
		return
	}

	var (
		wg             errgroup.Group
		userIdMap      = make(map[string]string)
		reqFileCodeMap = make(map[int32]string)
	)
	for _, req := range borrowReq {
		wg.Go(func() error {
			userId, userErr := larkclient.GetUserIDByEmail(ctx, req.CreatorEmail)
			if userErr != nil || userId == `` {
				logx.Error(`GetUserIDByEmail error:`, err, ` userId:`, userId)
				return userErr
			}
			userIdMap[req.CreatorEmail] = userId
			return nil
		})
		wg.Go(func() error {
			// 避免同一个附件重复上传到飞书
			redisCli := datasource.GetRedisDsCtxOrDefault(ctx).RedisCli
			fileCode, getErr := redisCli.HGet(ctx, rediscache.GetLarkSampleReqApprovalAttachmentHSetKey(), req.BorrowNo).Result()
			if getErr == nil && len(fileCode) > 0 {
				reqFileCodeMap[req.ID] = fileCode
				return nil
			}

			var (
				pdfUrl string
				q      = dal.DefaultQ(ctx)
			)
			err = q.SampleBorrowCertification.WithContext(ctx).
				Select(q.SampleBorrowCertification.CertificationChecklistPdf).
				Where(q.SampleBorrowCertification.RequestID.Eq(req.ID)).
				Scan(&pdfUrl)
			if err != nil || pdfUrl == `` {
				logx.Error(`query SampleBorrowCertificationChecklistPdf error:`, err)
				return err
			}
			// 从 oss 下载文件到本地
			filePath := `/tmp/SampleBorrow_` + req.BorrowNo + `.pdf`
			if err = downloadSampleListPdf(ctx, pdfUrl, filePath); err != nil {
				logx.Error(`download SampleBorrowCertificationChecklistPdf  from oss error:`, err)
				return err
			}
			defer func() {
				_ = os.Remove(filePath)
			}()
			// 上传文件到飞书
			fileCode, uploadErr := larkclient.UploadFile(ctx, filePath)
			// 删除临时文件
			if uploadErr != nil {
				logx.Error(`UploadFile error:`, err)
				return uploadErr
			}
			reqFileCodeMap[req.ID] = fileCode
			redisCli.HSet(ctx, rediscache.GetLarkSampleReqApprovalAttachmentHSetKey(), req.BorrowNo, fileCode)
			return nil
		})
	}
	if err = wg.Wait(); err != nil {
		logx.Errorf(`SyncSampleReq2LarkApproval get lark userId && upload file to lark error:%v`, err)
		return
	}
	// 同步飞书审批
	for _, req := range borrowReq {
		fileCode, ok := reqFileCodeMap[req.ID]
		if !ok {
			logx.Info(`invalid reqFileCode of sample request:`, req.ID)
			continue
		}
		userId, ok := userIdMap[req.CreatorEmail]
		if !ok {
			logx.Info(`failed to get lark user id from:`, req.CreatorEmail)
			continue
		}
		if !dal.GetSampleBorrowRequestEnhanced(ctx).Check(req.ID) {
			logx.Info(`invalid SampleBorrowRequest:`, req.ID)
			continue
		}

		instanceId, reqErr := larkclient.NewSampleBorrowApprovalInstanceReq(
			larkclient.SampleBorrowApprovalWithLarkUserID(userId),
			larkclient.SampleBorrowApprovalWithForms(larkclient.SampleBorrowApprovalInstanceForms{
				{
					ID:    `widget17531509086810001`, // 发起人
					Type:  `input`,
					Value: req.CreatorEmail,
				},
				{
					ID:    `widget17531505343440001`, // 借样申请单号
					Type:  `input`,
					Value: req.BorrowNo,
				},
				{
					ID:    `widget17531509114760001`, // 发起时间
					Type:  `input`,                   // 日期控件类型
					Value: req.CreateTime.Format(time.DateTime),
				},
				{
					ID:    `widget17531486303580001`, // 借样清单附件
					Type:  `attachmentV2`,
					Value: []string{fileCode},
				},
			}),
		).Do(ctx)
		if reqErr != nil {
			logx.Error(`SampleBorrowApprovalInstance request lark create approval instance error:`, reqErr)
			return
		}

		datasource.GetRedisDsCtxOrDefault(ctx).RedisCli.
			HDel(ctx, rediscache.GetLarkSampleReqApprovalAttachmentHSetKey(), req.BorrowNo)

		approval := model.SampleBorrowLarkApproval{
			RequestID:      int64(req.ID),
			InstanceID:     instanceId,
			ApplyUserMail:  req.CreatorEmail,
			ApprovalStatus: larkapproval.InstanceStatusPending,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}
		err = dal.GetSampleBorrowLarkApprovalEnhanced(ctx).Transaction(func(tx *query.Query) error {
			if err = tx.SampleBorrowLarkApproval.WithContext(ctx).
				Create(&approval); err != nil {
				return err
			}
			if _, err = tx.SampleBorrowRequest.WithContext(ctx).
				Where(tx.SampleBorrowRequest.ID.Eq(req.ID),
					tx.SampleBorrowRequest.Status.Eq(0)).
				Update(tx.SampleBorrowRequest.ThirdPartyStatus, 1); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			logx.Error(`db create sample borrow approval instance error:`, err, ` approval:`, approval)
		}
	}
	return

}

func downloadSampleListPdf(ctx context.Context, url string, filePath string) (err error) {
	var (
		ossCtx, cancel = context.WithTimeout(ctx, downloadTimeout)
		req, newErr    = http.NewRequestWithContext(ossCtx, `GET`, url, nil)
	)
	defer cancel()
	if newErr != nil {
		err = fmt.Errorf("创建请求失败: %w", newErr)
		logx.Error(err)
		return
	}
	// 发送请求
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse // 禁用自动重定向
		},
	}
	resp, err := client.Do(req)
	if err != nil {
		err = fmt.Errorf("请求失败: %w", err)
		logx.Error(err)
		return
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("无效状态码: %d", resp.StatusCode)
		logx.Error(err)
		return
	}

	// 限制读取大小
	limitedReader := &io.LimitedReader{
		R: resp.Body,
		N: 50 * 1024 * 1024, // 飞书限制单个附件大小 50M， 如果借样清单 pdf 下载超 50M 报错
	}
	// 读取内容
	data, err := io.ReadAll(limitedReader)
	if err != nil {
		err = fmt.Errorf("读取内容失败: %w", err)
		logx.Error(err)
		return
	}
	// 检查是否超出大小限制
	if limitedReader.N <= 0 {
		err = fmt.Errorf("文件大小超过 %dMB 限制", maxFileSize/1024/1024)
		logx.Error(err)
		return
	}
	err = saveToLocalFile(filePath, data)
	if err != nil {
		err = fmt.Errorf("saveToLocalFile error: %v", err)
		logx.Error(err)
		return
	}
	return nil
}

// CancelDirtyLarkApproval 扫描已经作废的借样申请，撤回飞书审批
func CancelDirtyLarkApproval(svcCtx *svc.ServiceContext, region string) {
	ctx := context.WithValue(context.Background(), consts.XLocaleHeaderKey, region)
	canceling, err := dal.GetSampleBorrowRequestEnhanced(ctx).GetSomeCanceling()
	if err != nil {
		logx.Error(`GetSomeCanceling error:`, err.Error())
		return
	}
	if len(canceling) == 0 {
		return
	}
	q := dal.DefaultQ(ctx)
	dirty, err := q.SampleBorrowLarkApproval.WithContext(ctx).
		Where(q.SampleBorrowLarkApproval.RequestID.In(canceling.IDs()...),
			q.SampleBorrowLarkApproval.ApprovalStatus.Eq(larkapproval.InstanceStatusPending),
			q.SampleBorrowLarkApproval.FlagDeprecated.Is(false)).
		Find()
	if err != nil {
		logx.Error(`db query SampleBorrowLarkApproval error:`, err)
		return
	}
	if len(dirty) == 0 {
		return
	}
	for _, approval := range dirty {
		go func() {
			cancelCtx, cancel := context.WithTimeout(ctx, time.Second*5)
			defer cancel()
			userId, userErr := larkclient.GetUserIDByEmail(ctx, approval.ApplyUserMail)
			if userErr != nil || userId == `` {
				logx.Error(`GetUserIDByEmail error:`, err, ` userId:`, userId)
				return
			}
			ok, _ := larkclient.CancelApprovalInstance(cancelCtx, userId, approval.InstanceID)
			if ok {
				_, err = q.SampleBorrowLarkApproval.WithContext(cancelCtx).
					Where(q.SampleBorrowLarkApproval.ID.Eq(approval.ID)).
					Update(q.SampleBorrowLarkApproval.FlagDeprecated, true)
				if err != nil {
					logx.Error(`update SampleBorrowLarkApproval FlagDeprecated error:`, err)
				}
				_, err = q.SampleBorrowRequest.WithContext(cancelCtx).
					Where(q.SampleBorrowRequest.ID.Eq(int32(approval.RequestID))).
					Update(q.SampleBorrowRequest.ThirdPartyStatus, 4)
				if err != nil {
					logx.Error(`update SampleBorrowRequest ThirdPartyStatus error:`, err)
				}
			}
		}()
	}
}

// SyncBackLarkApproval  扫描 pending > 30 min 的申请单实现飞书审批状态回传
func SyncBackLarkApproval(svcCtx *svc.ServiceContext, region string) {
	ctx := context.WithValue(context.Background(), `X-Locale`, region)
	pending, err := dal.GetSampleBorrowLarkApprovalEnhanced(ctx).GetSomePendingOVer30Min()
	if err != nil {
		logx.Error(`db query Pending BorrowLarkApproval Over 1 Hour error:`, err)
		return
	}
	if len(pending) == 0 {
		return
	}

	for _, p := range pending {
		userId, userErr := larkclient.GetUserIDByEmail(ctx, p.ApplyUserMail)
		if userErr != nil || userId == `` {
			logx.Error(`GetUserIDByEmail error:`, err, ` userId:`, userId)
			return
		}
		resp, getErr := larkclient.GetApprovalInstance(ctx, userId, p.InstanceID)
		if getErr != nil {
			logx.Error(`GetApprovalInstance error:`, getErr)
			return
		}
		if !resp.Success() {
			return
		}

		if resp.Status() == larkapproval.InstanceStatusPending {
			continue
		}

		q := dal.DefaultQ(ctx)
		_, err = q.SampleBorrowLarkApproval.WithContext(ctx).
			Where(q.SampleBorrowLarkApproval.ID.Eq(p.ID)).
			Update(q.SampleBorrowLarkApproval.ApprovalStatus, resp.Status())
		if err != nil {
			logx.Error(`update ApprovalInstance status error:`, err)
		}
		if status := larkclient.LarkStatus2Int[resp.Status()]; status > 0 {
			_, err = q.SampleBorrowRequest.WithContext(ctx).
				Where(q.SampleBorrowRequest.ID.Eq(int32(p.RequestID))).
				Update(q.SampleBorrowRequest.ThirdPartyStatus, status)
			if err != nil {
				logx.Error(`update ApprovalInstance status error:`, err)
			}
		}
	}
}


3. 飞书 approval template 有2个，一个是测试环境的，一个是生产环境的。
测试环境

[
  {
    "default_value_type": "CURRENT_VALUE",
    "display_condition": null,
    "enable_default_value": true,
    "id": "widget17539354871390001",
    "name": "申请人",
    "printable": true,
    "required": true,
    "type": "contact",
    "visible": true,
    "widget_default_value": "7521944677048172548"
  },
  {
    "default_value_type": "CURRENT_VALUE",
    "display_condition": null,
    "enable_default_value": true,
    "id": "widget17539355626810001",
    "name": "部门",
    "option": {
      "displayPath": false
    },
    "printable": true,
    "required": true,
    "type": "department",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539356603040001",
    "name": "项目名称",
    "printable": true,
    "required": true,
    "type": "input",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539356830320001",
    "name": "期望更新时间",
    "options": {
      "dateCheckEnd": 0,
      "dateCheckStart": 0,
      "dateCheckType": 9
    },
    "printable": true,
    "required": true,
    "type": "date",
    "value": "YYYY-MM-DD hh:mm",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "children": [
      {
        "default_value_type": "",
        "display_condition": null,
        "enable_default_value": false,
        "id": "widget17539358488910001",
        "name": "应用服务名称",
        "printable": true,
        "required": true,
        "type": "input",
        "visible": true,
        "widget_default_value": ""
      },
      {
        "default_value_type": "",
        "display_condition": null,
        "enable_default_value": false,
        "id": "widget17539358811410001",
        "name": "环境属性",
        "option": [
          {
            "value": "mdqw4zat-k4zkyjwoemq-0",
            "text": "生产PROD"
          },
          {
            "value": "mdqw4zat-4uuj04380fw-0",
            "text": "预发PRE"
          },
          {
            "value": "mdqw4zat-bqk72gq9wef-0",
            "text": "预发UAT"
          },
          {
            "value": "mdqw4zb5-beja9byz3ct-1",
            "text": "测试TEST"
          }
        ],
        "printable": true,
        "required": true,
        "type": "radioV2",
        "visible": true,
        "widget_default_value": ""
      },
      {
        "default_value_type": "",
        "display_condition": null,
        "enable_default_value": false,
        "id": "widget17539436410990001",
        "name": "分支名称",
        "printable": true,
        "required": false,
        "type": "input",
        "visible": true,
        "widget_default_value": ""
      },
      {
        "default_value_type": "",
        "display_condition": null,
        "enable_default_value": false,
        "id": "widget17539436691300001",
        "name": "版本TAG 信息",
        "printable": true,
        "required": false,
        "type": "input",
        "visible": true,
        "widget_default_value": ""
      },
      {
        "default_value_type": "",
        "display_condition": null,
        "enable_default_value": true,
        "id": "widget17539418080100001",
        "name": "是否涉及 SQL",
        "option": [
          {
            "value": "mdqzo0i2-oibl6xzh7e-0",
            "text": "是"
          },
          {
            "value": "mdqzo0i2-7t4es5yw8xo-0",
            "text": "否"
          }
        ],
        "printable": true,
        "required": true,
        "type": "radioV2",
        "visible": true,
        "widget_default_value": "mdqzo0i2-7t4es5yw8xo-0"
      },
      {
        "default_value_type": "",
        "display_condition": {
          "conditional": "OR",
          "conditions": [
            {
              "conditional": "AND",
              "conditions": null,
              "expressions": [
                {
                  "source_widget": {
                    "id": "widget17539418080100001",
                    "custom_id": ""
                  },
                  "compare_type": "is",
                  "standard_value": "mdqzo0i2-oibl6xzh7e-0"
                }
              ]
            }
          ],
          "expressions": null
        },
        "enable_default_value": false,
        "id": "widget17539418610280001",
        "name": "请上传SQL文件",
        "printable": true,
        "required": true,
        "type": "attachmentV2",
        "visible": true,
        "widget_default_value": ""
      }
    ],
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539358113720001",
    "name": "发布明细",
    "option": {
      "input_type": "FORM",
      "mobile_detail_type": "CARD",
      "print_type": "FORM"
    },
    "printable": true,
    "required": true,
    "type": "fieldList",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": true,
    "id": "widget17539419726120001",
    "name": "是否涉及域名变更",
    "option": [
      {
        "value": "mdqzrjid-cknby8n2j1j-0",
        "text": "是"
      },
      {
        "value": "mdqzrjid-fg3jno622mp-0",
        "text": "否"
      }
    ],
    "printable": true,
    "required": true,
    "type": "radioV2",
    "visible": true,
    "widget_default_value": "mdqzrjid-fg3jno622mp-0"
  },
  {
    "default_value_type": "",
    "display_condition": {
      "conditional": "OR",
      "conditions": [
        {
          "conditional": "AND",
          "conditions": null,
          "expressions": [
            {
              "source_widget": {
                "id": "widget17539419726120001",
                "custom_id": ""
              },
              "compare_type": "is",
              "standard_value": "mdqzrjid-cknby8n2j1j-0"
            }
          ]
        }
      ],
      "expressions": null
    },
    "enable_default_value": false,
    "id": "widget17539430031180001",
    "name": "关联域名申请审批",
    "printable": false,
    "required": true,
    "type": "connect",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": true,
    "id": "widget17539430964230001",
    "name": "是否提供发布操作手册",
    "option": [
      {
        "value": "mdr0fmnc-3nrsajhhzm5-0",
        "text": "是"
      },
      {
        "value": "mdr0fmnc-gtmaxlnxf5-0",
        "text": "否"
      }
    ],
    "printable": true,
    "required": true,
    "type": "radioV2",
    "visible": true,
    "widget_default_value": "mdr0fmnc-gtmaxlnxf5-0"
  },
  {
    "default_value_type": "",
    "display_condition": {
      "conditional": "OR",
      "conditions": [
        {
          "conditional": "AND",
          "conditions": null,
          "expressions": [
            {
              "source_widget": {
                "id": "widget17539430964230001",
                "custom_id": ""
              },
              "compare_type": "is",
              "standard_value": "mdr0fmnc-3nrsajhhzm5-0"
            }
          ]
        }
      ],
      "expressions": null
    },
    "enable_default_value": false,
    "id": "widget17539431844230001",
    "name": "请上传发布操作手册",
    "printable": true,
    "required": true,
    "type": "attachmentV2",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539432533930001",
    "name": "发布内容",
    "printable": true,
    "required": true,
    "type": "textarea",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539433318560001",
    "name": "知会项目组其他成员",
    "printable": true,
    "required": true,
    "type": "contact",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539433746720001",
    "name": "验收人",
    "printable": true,
    "required": true,
    "type": "contact",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": true,
    "id": "widget17539420080270001",
    "name": "测试结果",
    "option": [
      {
        "value": "mdqzsau3-g1v48v5k13j-0",
        "text": "通过"
      },
      {
        "value": "mdqzsau3-t8clampeycg-0",
        "text": "存在风险"
      }
    ],
    "printable": true,
    "required": true,
    "type": "radioV2",
    "visible": true,
    "widget_default_value": "mdqzsau3-g1v48v5k13j-0"
  },
  {
    "default_value_type": "",
    "display_condition": {
      "conditional": "OR",
      "conditions": [
        {
          "conditional": "AND",
          "conditions": null,
          "expressions": [
            {
              "source_widget": {
                "id": "widget17539420080270001",
                "custom_id": ""
              },
              "compare_type": "is",
              "standard_value": "mdqzsau3-t8clampeycg-0"
            }
          ]
        }
      ],
      "expressions": null
    },
    "enable_default_value": false,
    "id": "widget17539434166350001",
    "name": "测试风险描述",
    "printable": true,
    "required": true,
    "type": "textarea",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539434920780001",
    "name": "验收情况",
    "printable": true,
    "required": false,
    "type": "textarea",
    "visible": true,
    "widget_default_value": ""
  },
  {
    "default_value_type": "",
    "display_condition": null,
    "enable_default_value": false,
    "id": "widget17539435486250001",
    "name": "附件",
    "printable": true,
    "required": false,
    "type": "attachmentV2",
    "visible": true,
    "widget_default_value": ""
  }
]

生产环境
[{
	"default_value_type": "CURRENT_VALUE",
	"display_condition": null,
	"enable_default_value": true,
	"id": "widget17368477341570001",
	"name": "申请人",
	"printable": true,
	"required": true,
	"type": "contact",
	"visible": true,
	"widget_default_value": "7246230437633196034"
}, {
	"default_value_type": "CURRENT_VALUE",
	"display_condition": null,
	"enable_default_value": true,
	"id": "widget17369053623030001",
	"name": "部门",
	"option": {
		"displayPath": false
	},
	"printable": true,
	"required": true,
	"type": "department",
	"visible": true,
	"widget_default_value": "7243708055918182402"
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369053682060001",
	"name": "项目名称",
	"printable": true,
	"required": true,
	"type": "input",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369053862150001",
	"name": "期望更新时间",
	"options": {
		"dateCheckEnd": 0,
		"dateCheckStart": 0,
		"dateCheckType": 9
	},
	"printable": true,
	"required": true,
	"type": "date",
	"value": "YYYY-MM-DD hh:mm",
	"visible": true,
	"widget_default_value": ""
}, {
	"children": [{
		"default_value_type": "",
		"display_condition": null,
		"enable_default_value": false,
		"id": "widget17369054316090001",
		"name": "应用服务名称",
		"printable": true,
		"required": true,
		"type": "input",
		"visible": true,
		"widget_default_value": ""
	}, {
		"default_value_type": "",
		"display_condition": null,
		"enable_default_value": false,
		"id": "widget17441086436490001",
		"name": "环境属性",
		"option": [{
			"value": "m98d96ce-vcawm1357zp-0",
			"text": "生产PROD"
		}, {
			"value": "m98d96ce-kwjhta839hp-1",
			"text": "预发PRE"
		}, {
			"value": "m98d96ce-b4efs83xpth-2",
			"text": "预发UAT"
		}, {
			"value": "m98d96ce-n5v8i9shwj-3",
			"text": "测试TEST"
		}],
		"printable": true,
		"required": true,
		"type": "radioV2",
		"visible": true,
		"widget_default_value": ""
	}, {
		"default_value_type": "",
		"display_condition": null,
		"enable_default_value": false,
		"id": "widget17369054376400001",
		"name": "分支名称",
		"printable": true,
		"required": false,
		"type": "input",
		"visible": true,
		"widget_default_value": ""
	}, {
		"default_value_type": "",
		"display_condition": null,
		"enable_default_value": false,
		"id": "widget17369054465340001",
		"name": "版本TAG 信息",
		"printable": true,
		"required": false,
		"type": "input",
		"visible": true,
		"widget_default_value": ""
	}, {
		"default_value_type": "",
		"display_condition": null,
		"enable_default_value": true,
		"id": "widget17369054545280001",
		"name": "是否涉及 SQL",
		"option": [{
			"value": "m5x8nu0x-e4zwi5a2508-0",
			"text": "是"
		}, {
			"value": "m5x8nu0x-jkq2e159xl-0",
			"text": "否"
		}],
		"printable": true,
		"required": false,
		"type": "radioV2",
		"visible": true,
		"widget_default_value": "m5x8nu0x-jkq2e159xl-0"
	}, {
		"default_value_type": "",
		"display_condition": {
			"conditional": "OR",
			"conditions": [{
				"conditional": "AND",
				"conditions": null,
				"expressions": [{
					"source_widget": {
						"id": "widget17369054545280001",
						"custom_id": ""
					},
					"compare_type": "is",
					"standard_value": "m5x8nu0x-e4zwi5a2508-0"
				}]
			}],
			"expressions": null
		},
		"enable_default_value": false,
		"id": "widget17369056482370001",
		"name": "请上传SQL文件",
		"printable": true,
		"required": true,
		"type": "attachmentV2",
		"visible": true,
		"widget_default_value": ""
	}],
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369054208100001",
	"name": "发布明细",
	"option": {
		"input_type": "FORM",
		"mobile_detail_type": "CARD",
		"print_type": "FORM"
	},
	"printable": true,
	"required": true,
	"type": "fieldList",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": true,
	"id": "widget17369056840560001",
	"name": "是否涉及域名变更",
	"option": [{
		"value": "m5x8sr4o-7w530m0wne6-0",
		"text": "是"
	}, {
		"value": "m5x8sr4o-8t8w96hbqil-0",
		"text": "否"
	}],
	"printable": true,
	"required": true,
	"type": "radioV2",
	"visible": true,
	"widget_default_value": "m5x8sr4o-8t8w96hbqil-0"
}, {
	"default_value_type": "",
	"display_condition": {
		"conditional": "OR",
		"conditions": [{
			"conditional": "AND",
			"conditions": null,
			"expressions": [{
				"source_widget": {
					"id": "widget17369056840560001",
					"custom_id": ""
				},
				"compare_type": "is",
				"standard_value": "m5x8sr4o-7w530m0wne6-0"
			}]
		}],
		"expressions": null
	},
	"enable_default_value": false,
	"id": "widget17369057095740001",
	"name": "关联域名申请审批",
	"printable": false,
	"required": true,
	"type": "connect",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": true,
	"id": "widget17369058759370001",
	"name": "是否提供发布操作手册",
	"option": [{
		"value": "m5x8wv6q-fo0n9fdpwoh-0",
		"text": "是"
	}, {
		"value": "m5x8wv6q-88zz4dd3u0o-0",
		"text": "否"
	}],
	"printable": true,
	"required": true,
	"type": "radioV2",
	"visible": true,
	"widget_default_value": "m5x8wv6q-88zz4dd3u0o-0"
}, {
	"default_value_type": "",
	"display_condition": {
		"conditional": "OR",
		"conditions": [{
			"conditional": "AND",
			"conditions": null,
			"expressions": [{
				"source_widget": {
					"id": "widget17369058759370001",
					"custom_id": ""
				},
				"compare_type": "is",
				"standard_value": "m5x8wv6q-fo0n9fdpwoh-0"
			}]
		}],
		"expressions": null
	},
	"enable_default_value": false,
	"id": "widget17369059046830001",
	"name": "请上传发布操作手册",
	"printable": true,
	"required": true,
	"type": "attachmentV2",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369059255580001",
	"name": "发布内容",
	"printable": true,
	"required": true,
	"type": "textarea",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369059380980001",
	"name": "知会项目组其他成员",
	"printable": true,
	"required": true,
	"type": "contact",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369059615370001",
	"name": "验收人",
	"printable": true,
	"required": true,
	"type": "contact",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369059759100001",
	"name": "测试结果",
	"option": [{
		"value": "m5x8z0bq-adxldcpj3z9-0",
		"text": "通过"
	}, {
		"value": "m5x8z0bq-2io429ogtfk-0",
		"text": "存在风险"
	}],
	"printable": true,
	"required": true,
	"type": "radioV2",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": {
		"conditional": "OR",
		"conditions": [{
			"conditional": "AND",
			"conditions": null,
			"expressions": [{
				"source_widget": {
					"id": "widget17369059759100001",
					"custom_id": ""
				},
				"compare_type": "is",
				"standard_value": "m5x8z0bq-2io429ogtfk-0"
			}]
		}],
		"expressions": null
	},
	"enable_default_value": false,
	"id": "widget17369059919450001",
	"name": "测试风险描述",
	"printable": true,
	"required": true,
	"type": "textarea",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369060360660001",
	"name": "验收情况",
	"printable": true,
	"required": false,
	"type": "textarea",
	"visible": true,
	"widget_default_value": ""
}, {
	"default_value_type": "",
	"display_condition": null,
	"enable_default_value": false,
	"id": "widget17369060584600001",
	"name": "附件",
	"printable": true,
	"required": false,
	"type": "attachmentV2",
	"visible": true,
	"widget_default_value": ""
}]

// 测试环境
appId  = `cli_a8fa0c729a655013`
secret = `O2oUyiaNYA4CokDLsv90FbKWCBXTyMWC`
client = lark.NewClient(appId, secret)

生产环境
	FEISHU_APP_ID              = "cli_a7d56bbafb7e100b"
	FEISHU_APP_SECRET          = "0pZBo8iFDwb9veX27Y22ygVdRfEbNHoQ"