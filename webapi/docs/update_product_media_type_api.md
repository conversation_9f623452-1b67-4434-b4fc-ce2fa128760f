# 更新产品媒体类型 API

## 接口描述
更新 product_media 表中记录的 media_type 字段，将 `gallery_image` 更新为 `scm_gallery_image`，将 `white_image` 更新为 `scm_white_image`。如果 media_hash 为空，会自动计算并更新。

## 接口地址
```
PATCH /api/regions/{region}/maintenance/product-media/{id}/media-type
```

## 请求参数

### 路径参数
- `region` (string, required): 区域标识
- `id` (string, required): product_media 记录的 ID

### 请求体
```json
{
  "mediaType": "string"  // 可选，当前版本未使用此字段
}
```

## 响应格式
```json
{
  "success": boolean,
  "errorMessage": "string"
}
```

## 业务逻辑

1. **查询记录**: 根据 ID 查询 product_media 记录，如果不存在返回错误
2. **检查状态**: 如果 media_type 已经是 `scm_white_image` 或 `scm_gallery_image`，则跳过处理
3. **更新类型**: 
   - `gallery_image` → `scm_gallery_image`
   - `white_image` → `scm_white_image`
   - 其他类型返回错误
4. **计算哈希**: 如果 media_hash 为空，从 media_url 下载文件并计算 MD5 哈希值
5. **保存更新**: 将更新后的记录保存到数据库

## 示例

### 请求示例
```bash
curl -X PATCH "https://api.example.com/api/regions/US/maintenance/product-media/12345/media-type" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 成功响应
```json
{
  "success": true,
  "errorMessage": "更新成功"
}
```

### 错误响应
```json
{
  "success": false,
  "errorMessage": "查询产品媒体失败: record not found"
}
```

## 错误码说明

- `无效的ID格式`: ID 参数不是有效的数字
- `查询产品媒体失败`: 数据库查询失败或记录不存在
- `该媒体类型已经处理过，无需重复处理`: media_type 已经是目标值
- `不支持的媒体类型`: media_type 不是 gallery_image 或 white_image
- `更新产品媒体失败`: 数据库更新操作失败

## 注意事项

1. 哈希计算失败不会影响主流程，只会记录日志
2. 文件下载超时时间为 30 秒
3. 支持的媒体类型转换：
   - `gallery_image` → `scm_gallery_image`
   - `white_image` → `scm_white_image`
4. 已处理过的记录（media_type 为 scm_* 开头）会被跳过
