# 飞书审批功能测试指南

## 功能概述
为发布计划创建飞书审批的API已经实现完成。

## API 端点
```
POST /plans/:plan_id/feishu-approvals
```

## 前置条件
1. 发布计划必须存在
2. 发布计划的内部审批必须已完成（status = "approved"）
3. 发布计划尚未创建过飞书审批
4. 用户必须有有效的邮箱，且能在飞书中找到对应用户

## 配置文件设置
飞书配置已集成到配置文件中，无需设置环境变量。

### 配置文件示例 (webapi.yaml)
```yaml
FeiShu:
  AppId: cli_a8fa0c729a655013
  AppSecret: O2oUyiaNYA4CokDLsv90FbKWCBXTyMWC
  ReleaseApprovalCode: 44E6EEF4-E866-432A-951E-8A10B9017B0C
```

### 生产环境配置
生产环境需要使用不同的 AppId、AppSecret 和 ReleaseApprovalCode。

## 数据库迁移
确保已执行以下迁移：
```sql
-- 添加飞书审批实例ID字段
ALTER TABLE release_plans 
ADD COLUMN feishu_approval_instance_id VARCHAR(255) DEFAULT '' COMMENT '飞书审批实例ID';

-- 添加索引
CREATE INDEX idx_release_plans_feishu_approval_instance_id 
ON release_plans(feishu_approval_instance_id);
```

## 表单字段说明

飞书审批表单包含以下字段：

1. **项目名称** - 固定为 "电商系统-" + 发布计划名称
2. **期望更新时间** - 固定设置为当天的 18:30
3. **发布明细** - 包含所有应用任务的详情：
   - 应用服务名称：`jenkins_job_full_path-(name)`
   - 环境属性：固定为 "生产PROD"
   - 分支名称：来自 release_application_tasks 的 git_branch
   - 是否涉及SQL：固定为 "否"
4. **发布内容** - release plan 的 description + 所有相关URLs（包括发布手册、MR、PRD、技术设计等）
5. **知会项目组其他成员** - 固定设置为 <EMAIL>
6. **验收人** - 固定设置为 <EMAIL>
7. **测试结果** - 固定设置为 "通过"

### 环境自动识别
系统会根据配置中的 FeiShu.AppId 自动识别环境：
- **生产环境**: `cli_a7d56bbafb7e100b` - 使用生产环境的 widget ID 和选项值
- **测试环境**: `cli_a8fa0c729a655013` - 使用测试环境的 widget ID 和选项值

### 模板化实现
- 所有 widget ID 和选项值都存储在 `approval_templates.go` 中
- 支持测试环境和生产环境的不同模板配置
- 使用通用的表单构建函数，根据环境参数动态生成表单

### 自动处理的字段
- **是否涉及域名变更** - 不设置，使用飞书审批模板默认值
- **是否提供发布操作手册** - 不设置，使用飞书审批模板默认值

表单数据以 JSON 数组格式提交到飞书审批系统。

## 测试步骤

### 1. 创建发布计划
首先创建一个发布计划并完成内部审批流程。

### 2. 调用飞书审批API
```bash
curl -X POST "http://localhost:8888/plans/{plan_id}/feishu-approvals" \
  -H "Content-Type: application/json" \
  -H "ec-username: <EMAIL>"
```

### 3. 验证结果
- 检查返回的响应是否为成功状态
- 检查数据库中 release_plans 表的 feishu_approval_instance_id 字段是否已更新
- 在飞书中查看是否创建了对应的审批实例

## 预期响应

### 成功响应
```json
{
  "code": 0,
  "msg": "创建飞书审批成功"
}
```

### 错误响应示例
```json
{
  "code": 404,
  "msg": "发布计划不存在"
}
```

```json
{
  "code": 400,
  "msg": "发布计划内部审批未完成，无法创建飞书审批"
}
```

```json
{
  "code": 400,
  "msg": "该发布计划已创建飞书审批"
}
```

## 实现细节

### 主要组件
1. **API Handler**: `webapi/internal/handler/release/createFeishuApprovalForReleasePlanHandler.go`
2. **业务逻辑**: `webapi/internal/logic/release/createFeishuApprovalForReleasePlanLogic.go`
3. **飞书服务**: `webapi/internal/third/lark/lark_service.go`
4. **数据模型**: `webapi/internal/repository/release_plans.go`

### 审批表单字段
根据飞书审批模板，表单包含以下字段：
- 项目名称（发布计划名称）
- 申请人（创建人邮箱）
- 期望更新时间（创建时间）

### 错误处理
- 发布计划不存在
- 内部审批未完成
- 已存在飞书审批实例
- 用户邮箱无效或无法获取飞书用户ID
- 飞书API调用失败

## 注意事项
1. 审批模板代码需要根据实际的飞书审批模板进行调整
2. 表单字段ID需要与飞书审批模板中的控件ID匹配
3. 确保飞书应用有足够的权限创建审批实例
4. 建议在测试环境充分测试后再部署到生产环境
