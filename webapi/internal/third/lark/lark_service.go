package lark

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/zeromicro/go-zero/core/logx"
)

// LarkService 飞书服务
type LarkService struct {
	client *lark.Client
	logger logx.Logger
}

// LarkConfig 飞书配置
type LarkConfig struct {
	AppId     string
	AppSecret string
}

// NewLarkService 创建飞书服务
func NewLarkService(config LarkConfig) *LarkService {
	client := lark.NewClient(config.AppId, config.AppSecret)
	return &LarkService{
		client: client,
		logger: logx.WithContext(context.Background()),
	}
}

// GetUserIDByEmail 根据邮箱获取用户ID
func (s *LarkService) GetUserIDByEmail(ctx context.Context, email string) (string, error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().UserIdType(larkapproval.UserIdTypeUserId).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails([]string{email}).
			Build()).
		Build()

	resp, err := s.client.Contact.User.BatchGetId(ctx, req)
	if err != nil {
		s.logger.Errorf("获取用户ID失败: %v", err)
		return "", fmt.Errorf("获取用户ID失败: %w", err)
	}

	if !resp.Success() {
		s.logger.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		return "", fmt.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	if len(resp.Data.UserList) == 0 {
		return "", fmt.Errorf("未找到邮箱 %s 对应的用户", email)
	}

	userId := resp.Data.UserList[0].UserId
	if userId == nil {
		return "", fmt.Errorf("用户ID为空")
	}

	return *userId, nil
}

// CreateApprovalInstance 创建审批实例
func (s *LarkService) CreateApprovalInstance(ctx context.Context, req *CreateApprovalInstanceRequest) (string, error) {
	// 构建审批实例请求 - 使用正确的API结构
	createReq := larkapproval.NewCreateInstanceReqBuilder().
		InstanceCreate(larkapproval.NewInstanceCreateBuilder().
			ApprovalCode(req.ApprovalCode).
			UserId(req.UserId).
			Form(req.Form).
			Build()).
		Build()

	resp, err := s.client.Approval.Instance.Create(ctx, createReq)
	if err != nil {
		s.logger.Errorf("创建审批实例失败: %v", err)
		return "", fmt.Errorf("创建审批实例失败: %w", err)
	}

	if !resp.Success() {
		s.logger.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		return "", fmt.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	if resp.Data == nil || resp.Data.InstanceCode == nil {
		return "", fmt.Errorf("审批实例ID为空")
	}

	return *resp.Data.InstanceCode, nil
}

// GetApprovalInstance 获取审批实例详情
func (s *LarkService) GetApprovalInstance(ctx context.Context, userId, instanceId string) (*larkapproval.GetInstanceResp, error) {
	req := larkapproval.NewGetInstanceReqBuilder().
		InstanceId(instanceId).
		UserId(userId).
		Build()

	resp, err := s.client.Approval.Instance.Get(ctx, req)
	if err != nil {
		s.logger.Errorf("获取审批实例失败: %v", err)
		return nil, fmt.Errorf("获取审批实例失败: %w", err)
	}

	if !resp.Success() {
		s.logger.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		return nil, fmt.Errorf("飞书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	return resp, nil
}

// CreateApprovalInstanceRequest 创建审批实例请求
type CreateApprovalInstanceRequest struct {
	ApprovalCode string
	UserId       string
	Form         string // JSON格式的表单数据
}

// ReleasePlanApprovalForm 发布计划审批表单
type ReleasePlanApprovalForm struct {
	PlanName         string                  // 发布计划名称
	Description      string                  // 发布计划描述
	AllUrls          []string                // 所有相关URL
	ApplicationTasks []ApplicationTaskDetail // 应用任务详情
	CreatedBy        string                  // 创建人
}

// ApplicationTaskDetail 应用任务详情
type ApplicationTaskDetail struct {
	ServiceName string // 应用服务名称：jenkins_job_full_path-(name)
	GitBranch   string // 分支名称
}

// BuildReleasePlanApprovalFormForTest 构建发布计划审批表单 - 测试环境
func BuildReleasePlanApprovalFormForTest(form ReleasePlanApprovalForm, duanwuxueUserId string) string {
	return buildReleasePlanApprovalForm(form, duanwuxueUserId, TestEnvironmentWidgets, TestEnvironmentOptions)
}

// BuildReleasePlanApprovalFormForProd 构建发布计划审批表单 - 生产环境
func BuildReleasePlanApprovalFormForProd(form ReleasePlanApprovalForm, duanwuxueUserId string) string {
	return buildReleasePlanApprovalForm(form, duanwuxueUserId, ProductionEnvironmentWidgets, ProductionEnvironmentOptions)
}

// buildReleasePlanApprovalForm 通用的表单构建函数
func buildReleasePlanApprovalForm(form ReleasePlanApprovalForm, duanwuxueUserId string, widgets map[string]string, options map[string]string) string {
	// 1. 项目名称：电商系统 + "-" + 发布计划名称
	projectName := "电商系统-" + form.PlanName

	// 2. 期望更新时间：当天的 18:30
	now := time.Now()
	expectedTime := fmt.Sprintf("%s 18:30", now.Format("2006-01-02"))

	// 3. 构建发布明细 - fieldList 格式
	var releaseDetailsList []map[string]interface{}
	for _, task := range form.ApplicationTasks {
		releaseDetailsList = append(releaseDetailsList, map[string]interface{}{
			widgets["service_name"]: task.ServiceName,    // 应用服务名称
			widgets["environment"]:  options["env_prod"], // 环境属性：生产PROD
			widgets["branch_name"]:  task.GitBranch,      // 分支名称
			widgets["has_sql"]:      options["sql_no"],   // 是否涉及SQL：否
		})
	}

	// 4. 发布内容：description + 所有URLs
	var releaseContent strings.Builder
	releaseContent.WriteString(form.Description)
	if len(form.AllUrls) > 0 {
		releaseContent.WriteString("\n\n相关链接：\n")
		for _, url := range form.AllUrls {
			releaseContent.WriteString("- " + url + "\n")
		}
	}

	// 构建表单JSON
	formData := fmt.Sprintf(`[
		{
			"id": "%s",
			"type": "input",
			"value": "%s"
		},
		{
			"id": "%s",
			"type": "date",
			"value": "%s"
		},
		{
			"id": "%s",
			"type": "fieldList",
			"value": %s
		},
		{
			"id": "%s",
			"type": "textarea",
			"value": "%s"
		},
		{
			"id": "%s",
			"type": "contact",
			"value": "%s"
		},
		{
			"id": "%s",
			"type": "contact",
			"value": "%s"
		},
		{
			"id": "%s",
			"type": "radioV2",
			"value": "%s"
		}
	]`,
		widgets["project_name"], projectName,
		widgets["expected_time"], expectedTime,
		widgets["release_details"], mustMarshalJSON(releaseDetailsList),
		widgets["release_content"], releaseContent.String(),
		widgets["notify_members"], duanwuxueUserId,
		widgets["acceptor"], duanwuxueUserId,
		widgets["test_result"], options["test_pass"],
	)

	return formData
}

// mustMarshalJSON 辅助函数，用于JSON序列化
func mustMarshalJSON(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		return "[]"
	}
	return string(data)
}
