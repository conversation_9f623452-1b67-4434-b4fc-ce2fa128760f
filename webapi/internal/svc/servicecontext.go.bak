package svc

import (
	"log"
	"time"
	"webapi/internal/config"
	"webapi/internal/middleware"
	"webapi/internal/nacosconfig"
	"webapi/internal/repository"
	"webapi/internal/service"
	"webapi/internal/service/approval"
	"webapi/internal/third/financial"
	"webapi/internal/third/gitlab"
	"webapi/internal/third/jenkins"
	"webapi/internal/third/k8s"
	"webapi/internal/third/lark"

	"git.blueorigin.work/e-commerce/shopping-common/job"

	commonhttp "git.blueorigin.work/e-commerce/shopping-common/common/http"
	"github.com/go-resty/resty/v2"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config           config.Config
	Server           *rest.Server
	AuthInterceptor  rest.Middleware
	RestyClient      *resty.Client
	JobExecutor      *job.Executor
	FinancialService financial.FinancialService
	GitLabSvc        gitlab.GitLabService
	JenkinsSvc       jenkins.JenkinsService
	K8sService       k8s.K8sService
	LarkSvc          *lark.LarkService

	// 使用泛型配置管理器
	AppConfigManager            *nacosconfig.Manager[config.AppConfig]
	AclConfigManager            *nacosconfig.Manager[config.AclRootConfig]
	OndutyConfigManager         *nacosconfig.Manager[config.OndutyRootConfig]
	GrabConfigManager           *nacosconfig.Manager[config.GrabRootConfig]
	FeatureControlConfigManager *nacosconfig.Manager[config.FeatureControlConfig]

	InfluencerVideoRepo                   *repository.InfluencerVideoRepository
	InfluencerVideoDailyMetricsRepository *repository.InfluencerVideoDailyMetricsRepository
	InfluencerVideoDatasourceRepo         *repository.InfluencerVideoDatasourceRepository
	LivePerformanceRepo                   *repository.LivePerformanceRepository
	LivePerformanceDatasourceRepo         *repository.LivePerformanceDatasourceRepository
	LivePerformanceGoalRepo               *repository.LivePerformanceGoalRepository
	UserRepo                              *repository.UserRepository
	ApplicationRepo                       *repository.ApplicationRepository
	ReleasePlanRepo                       *repository.ReleasePlanRepository
	ReleaseApplicationTaskRepo            *repository.ReleaseApplicationTaskRepository
	ReleaseDbMigrationTaskRepo            *repository.ReleaseDbMigrationTaskRepository
	ApprovalInstanceRepo                  *repository.ApprovalInstanceRepository
	ApprovalFlowRepo                      *repository.ApprovalFlowRepository
	ApprovalFlowNodeRepo                  *repository.ApprovalFlowNodeRepository
	ApprovalTaskRepo                      *repository.ApprovalTaskRepository
	ApprovalService                       *approval.ApprovalService
	JenkinsBuildNumberSyncService         *service.JenkinsBuildNumberSyncService
	RocketMQService                       *service.RocketMQService
}

func NewServiceContext(c config.Config, server *rest.Server) *ServiceContext {
	// 创建带有指标监控的 HTTP 客户端
	restyClient := resty.New()
	httpClient := commonhttp.GetClient(nil)
	restyClient.SetTransport(httpClient.Transport)
	restyClient.SetTimeout(httpClient.Timeout)

	// 创建 Nacos 服务器配置
	serverConfig := &constant.ServerConfig{
		IpAddr: c.Nacos.IpAddr,
		Port:   c.Nacos.Port,
		Scheme: c.Nacos.Scheme,
	}

	// 创建 Nacos 客户端配置
	clientConfig := &constant.ClientConfig{
		NamespaceId:         c.Nacos.NamespaceId,
		TimeoutMs:           3000,
		NotLoadCacheAtStart: true, // Disable cache loading at start
		LogLevel:            "debug",
	}

	// 初始化泛型配置管理器
	appConfigManager, err := nacosconfig.NewManager[config.AppConfig](serverConfig, clientConfig, nacosconfig.NACOS_DATA_ID_APPS, nacosconfig.NACOS_GROUP_COMMON)
	if err != nil {
		log.Fatalf("创建应用配置管理器失败: %v", err)
	}
	// ACL 配置管理器
	aclConfigManager, err := nacosconfig.NewManager[config.AclRootConfig](serverConfig, clientConfig, nacosconfig.NACOS_DATA_ID_ACL, nacosconfig.NACOS_GROUP_EC)
	if err != nil {
		log.Fatalf("创建 ACL 配置管理器失败: %v", err)
	}
	// Oncall 配置管理器
	ondutyConfigManager, err := nacosconfig.NewManager[config.OndutyRootConfig](serverConfig, clientConfig, nacosconfig.NACOS_DATA_ID_ONDUTY, nacosconfig.NACOS_GROUP_COMMON)
	if err != nil {
		log.Fatalf("创建 Oncall 配置管理器失败: %v", err)
	}
	// Grab 配置管理器
	grabConfigManager, err := nacosconfig.NewManager[config.GrabRootConfig](&constant.ServerConfig{
		IpAddr: c.O2O.Nacos.IpAddr,
		Port:   c.O2O.Nacos.Port,
		Scheme: "https",
	}, &constant.ClientConfig{
		NamespaceId:         c.O2O.Nacos.NamespaceId,
		TimeoutMs:           3000,
		NotLoadCacheAtStart: true, // Disable cache loading at start
		LogLevel:            "debug",
	}, nacosconfig.RETAIL_NACOS_DATA_ID_GRAB, nacosconfig.RETAIL_NACOS_GROUP_O2O)
	if err != nil {
		log.Fatalf("创建 Grab 配置管理器失败: %v", err)
	}
	// 特性开关配置管理器
	featureControlConfigManager, err := nacosconfig.NewManager[config.FeatureControlConfig](serverConfig, clientConfig, nacosconfig.NACOS_DATA_ID_FEATURE_CONTROL, nacosconfig.NACOS_GROUP_COMMON)
	if err != nil {
		log.Fatalf("创建特性开关配置管理器失败: %v", err)
	}

	xxlConf := &job.XxlJobConfig{
		ServerAddr:  c.XxlJobConf.ServerAddr,
		AccessToken: c.XxlJobConf.AccessToken,
		// todo debug 注意删除
		ExecutorPort: "8888",
		AppName:      c.XxlJobConf.AppName,
	}

	gitlabSvc, err := gitlab.NewGitLabService(&gitlab.GitLabConfig{
		Host:    c.GitLab.Host,
		Token:   c.GitLab.Token,
		Timeout: 10 * time.Second,
	})
	if err != nil {
		log.Fatalln(err)
	}

	jenkinsSvc, err := jenkins.NewJenkinsService(c.Jenkins.BaseURL, c.Jenkins.User, c.Jenkins.Token)
	if err != nil {
		log.Fatalln(err)
	}

	k8sService, err := k8s.NewK8sService(c.Kubernetes.KubeConfig, c.Kubernetes.Context)
	if err != nil {
		log.Fatalln("Failed to create Kubernetes service:", err)
	}

	// 创建 RocketMQ 服务
	rocketMQService, err := service.NewRocketMQService(service.RocketMQConfig{
		InstanceId: c.RocketMQ.InstanceId,
		RegionId:   c.RocketMQ.RegionId,
		AccessKey:  c.RocketMQ.AccessKey,
		SecretKey:  c.RocketMQ.SecretKey,
		Timeout:    c.RocketMQ.Timeout,
	})
	if err != nil {
		log.Printf("Warning: Failed to create RocketMQ service: %v", err)
		// 不要让RocketMQ服务创建失败影响整个应用启动
		rocketMQService = nil
	}

	ctx := &ServiceContext{
		Config:                      c,
		Server:                      server,
		AppConfigManager:            appConfigManager,
		AclConfigManager:            aclConfigManager,
		OndutyConfigManager:         ondutyConfigManager,
		GrabConfigManager:           grabConfigManager,
		FeatureControlConfigManager: featureControlConfigManager,
		AuthInterceptor:             middleware.NewAuthInterceptorMiddleware(appConfigManager, aclConfigManager, server).Handle,
		RestyClient:                 restyClient,
		JobExecutor:                 job.NewJobExecutor(xxlConf),
		FinancialService: financial.NewFinancialService(
			c.Financial.Host,
			c.Financial.AppID,
			c.Financial.AppSecret,
			c.Financial.AuthURL,
			c.Financial.ChainURL,
		),
		GitLabSvc:  gitlabSvc,
		JenkinsSvc: jenkinsSvc,
		K8sService: k8sService,
		LarkSvc: lark.NewLarkService(lark.LarkConfig{
			AppId:               c.FeiShu.AppId,
			AppSecret:           c.FeiShu.AppSecret,
			ReleaseApprovalCode: c.FeiShu.ReleaseApprovalCode,
		}),
		RocketMQService:                       rocketMQService,
		InfluencerVideoRepo:                   repository.NewInfluencerVideoRepository(),
		InfluencerVideoDailyMetricsRepository: repository.NewInfluencerVideoDailyMetricsRepository(),
		InfluencerVideoDatasourceRepo:         repository.NewInfluencerVideoDatasourceRepository(),
		LivePerformanceRepo:                   repository.NewLivePerformanceRepository(),
		LivePerformanceDatasourceRepo:         repository.NewLivePerformanceDatasourceRepository(),
		LivePerformanceGoalRepo:               repository.NewLivePerformanceGoalRepository(),
		UserRepo:                              repository.NewUserRepository(),
		ApplicationRepo:                       repository.NewApplicationRepository(),
		ReleasePlanRepo:                       repository.NewReleasePlanRepository(),
		ReleaseApplicationTaskRepo:            repository.NewReleaseApplicationTaskRepository(),
		ReleaseDbMigrationTaskRepo:            repository.NewReleaseDbMigrationTaskRepository(),
		ApprovalInstanceRepo:                  repository.NewApprovalInstanceRepository(),
		ApprovalFlowRepo:                      repository.NewApprovalFlowRepository(),
		ApprovalFlowNodeRepo:                  repository.NewApprovalFlowNodeRepository(),
		ApprovalTaskRepo:                      repository.NewApprovalTaskRepository(),
		ApprovalService:                       approval.NewApprovalService(&c),
	}

	// 初始化 Jenkins Build Number 同步服务
	ctx.JenkinsBuildNumberSyncService = service.NewJenkinsBuildNumberSyncService(
		ctx.ReleaseApplicationTaskRepo,
		ctx.ApplicationRepo,
		ctx.JenkinsSvc,
	)

	return ctx
}
