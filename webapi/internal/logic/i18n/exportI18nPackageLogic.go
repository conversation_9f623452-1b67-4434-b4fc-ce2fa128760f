package i18n

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"strconv"
	"strings"
	"time"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/zeromicro/go-zero/core/logx"
)

type ExportI18nPackageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// I18nPackageMeta 语言包元数据结构
type I18nPackageMeta struct {
	Version            string   `json:"version"`            // 语言包版本
	GeneratedAt        string   `json:"generatedAt"`        // 生成时间
	Namespace          string   `json:"namespace"`          // 命名空间
	Languages          []string `json:"languages"`          // 包含的语言列表
	IOSAppVersions     []string `json:"iosAppVersions"`     // 适用的 iOS App 版本列表
	AndroidAppVersions []string `json:"androidAppVersions"` // 适用的 Android App 版本列表
}

// I18nPackageData 语言包数据结构
type I18nPackageData struct {
	Meta I18nPackageMeta              `json:"meta"` // 元数据
	Data map[string]map[string]string `json:"data"` // 语言数据: map[languageCode]map[key]value
}

// StsCredentials STS 临时凭证
type StsCredentials struct {
	AccessKeyId     string
	AccessKeySecret string
	SecurityToken   string
}

func NewExportI18nPackageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportI18nPackageLogic {
	return &ExportI18nPackageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportI18nPackageLogic) ExportI18nPackage(req *types.ExportI18nPackageReq) (resp *types.ExportI18nPackageResp, err error) {
	// 参数验证
	if req.Tag == "" {
		return &types.ExportI18nPackageResp{
			Success: false,
			Message: "版本标签不能为空",
		}, nil
	}

	if len(req.Languages) == 0 {
		return &types.ExportI18nPackageResp{
			Success: false,
			Message: "语言列表不能为空",
		}, nil
	}

	// 设置默认值
	if req.Namespace == "" {
		req.Namespace = "ohsome-app"
	}

	l.Infof("开始导出语言包，参数: tag=%s, namespace=%s, languages=%v", req.Tag, req.Namespace, req.Languages)

	// 导出 Android 和 iOS 语言包
	devices := []string{"android", "ios"}
	var androidUrl, iosUrl string

	for _, device := range devices {
		url, err := l.exportJSONLanguagePackage(req, device)
		if err != nil {
			l.Errorf("导出 %s 语言包失败: %v", device, err)
			return &types.ExportI18nPackageResp{
				Success: false,
				Message: fmt.Sprintf("导出 %s 语言包失败: %v", device, err),
			}, nil
		}

		if device == "android" {
			androidUrl = url
		} else {
			iosUrl = url
		}

		l.Infof("成功导出 %s 语言包: %s", device, url)
	}

	return &types.ExportI18nPackageResp{
		Success: true,
		Message: "导出成功",
		Data: types.ExportI18nPackageData{
			AndroidUrl: androidUrl,
			IOSUrl:     iosUrl,
		},
	}, nil
}

// exportJSONLanguagePackage 导出JSON格式的语言包，包含 meta 和 data 字段
func (l *ExportI18nPackageLogic) exportJSONLanguagePackage(req *types.ExportI18nPackageReq, device string) (string, error) {
	// 使用现有的导出逻辑获取所有语言的内容
	exportLogic := NewExportTranslationsLogic(l.ctx, l.svcCtx)

	// 构建语言数据: map[languageCode]map[key]value
	languageData := make(map[string]map[string]string)

	for _, language := range req.Languages {
		exportResp, err := exportLogic.ExportTranslations(&types.ExportTranslationsReq{
			LanguageCode: language,
			Namespace:    req.Namespace,
			Device:       device,
		})

		if err != nil {
			return "", fmt.Errorf("导出 %s %s 失败: %v", device, language, err)
		}

		// 将原始内容转换为map[key]value
		var languageMap map[string]string
		switch device {
		case "android":
			languageMap, err = l.parseAndroidXMLToMap(exportResp.Data)
		case "ios":
			languageMap, err = l.parseIOSStringsToMap(exportResp.Data)
		default:
			err = fmt.Errorf("不支持的设备类型: %s", device)
		}

		if err != nil {
			return "", fmt.Errorf("解析 %s %s 内容失败: %v", device, language, err)
		}

		languageData[language] = languageMap
		l.Infof("成功解析 %s %s 语言包，包含 %d 个键值对", device, language, len(languageMap))
	}

	// 构建完整的语言包数据结构
	packageData := I18nPackageData{
		Meta: I18nPackageMeta{
			Version:            req.Tag,
			GeneratedAt:        time.Now().Format(time.RFC3339),
			Namespace:          req.Namespace,
			Languages:          req.Languages,
			IOSAppVersions:     req.IOSAppVersions,
			AndroidAppVersions: req.AndroidAppVersions,
		},
		Data: languageData,
	}

	// 生成最终的JSON内容
	jsonData, err := json.MarshalIndent(packageData, "", "  ")
	if err != nil {
		return "", fmt.Errorf("生成JSON失败: %v", err)
	}

	// 生成文件名和路径：i18n/${device}/${version_or_tag}.json
	fileName := fmt.Sprintf("%s.json", req.Tag)
	ossPath := fmt.Sprintf("i18n/%s/%s", device, fileName)

	// 上传到 OSS
	_, err = l.uploadToOSS(ossPath, string(jsonData))
	if err != nil {
		return "", fmt.Errorf("上传到 OSS 失败: %v", err)
	}

	// 构造 CDN 链接
	cdnUrl := fmt.Sprintf("https://ec-cdn.blueorigin.cn/%s", ossPath)
	return cdnUrl, nil
}

// parseAndroidXMLToMap 解析Android XML格式为map[key]value
func (l *ExportI18nPackageLogic) parseAndroidXMLToMap(xmlContent string) (map[string]string, error) {
	// 解析XML内容
	type StringEntry struct {
		Name  string `xml:"name,attr"`
		Value string `xml:",chardata"`
	}

	type Resources struct {
		Strings []StringEntry `xml:"string"`
	}

	var resources Resources
	err := xml.Unmarshal([]byte(xmlContent), &resources)
	if err != nil {
		return nil, fmt.Errorf("解析Android XML失败: %v", err)
	}

	// 转换为map
	resultMap := make(map[string]string)
	for _, str := range resources.Strings {
		if str.Name != "" && str.Value != "" {
			resultMap[str.Name] = str.Value
		}
	}

	return resultMap, nil
}

// parseIOSStringsToMap 解析iOS .strings格式为map[key]value
func (l *ExportI18nPackageLogic) parseIOSStringsToMap(stringsContent string) (map[string]string, error) {
	resultMap := make(map[string]string)

	// 解析.strings文件内容
	lines := strings.Split(stringsContent, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "//") {
			continue // 跳过空行和注释
		}

		// 解析格式: "key" = "value";
		if strings.Contains(line, "=") && strings.HasSuffix(line, ";") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])

				// 移除引号和分号
				key = strings.Trim(key, "\"")
				value = strings.TrimSuffix(value, ";")
				value = strings.TrimSpace(value)
				value = strings.Trim(value, "\"")

				if key != "" && value != "" {
					resultMap[key] = value
				}
			}
		}
	}

	return resultMap, nil
}

// uploadToOSS 上传文件到 OSS，使用 STS 临时凭证
func (l *ExportI18nPackageLogic) uploadToOSS(ossPath, content string) (int64, error) {
	// 获取 STS 临时凭证
	stsCredentials, err := l.getStsCredentials()
	if err != nil {
		return 0, fmt.Errorf("获取 STS 凭证失败: %v", err)
	}

	// 使用 STS 临时凭证创建 OSS 客户端
	client, err := oss.New(l.svcCtx.Config.OSS.Endpoint, stsCredentials.AccessKeyId, stsCredentials.AccessKeySecret, oss.SecurityToken(stsCredentials.SecurityToken))
	if err != nil {
		return 0, fmt.Errorf("创建 OSS 客户端失败: %v", err)
	}

	// 获取 bucket
	bucket, err := client.Bucket(l.svcCtx.Config.OSS.BucketName)
	if err != nil {
		return 0, fmt.Errorf("获取 OSS bucket 失败: %v", err)
	}

	// 上传文件
	contentBytes := []byte(content)
	err = bucket.PutObject(ossPath, strings.NewReader(content))
	if err != nil {
		return 0, fmt.Errorf("上传文件到 OSS 失败: %v", err)
	}

	l.Infof("成功上传文件到 OSS: %s", ossPath)
	return int64(len(contentBytes)), nil
}

// getStsCredentials 获取 STS 临时凭证
func (l *ExportI18nPackageLogic) getStsCredentials() (*StsCredentials, error) {
	// 创建 STS 客户端
	config := sdk.NewConfig()
	credential := credentials.NewAccessKeyCredential(l.svcCtx.Config.OSS.AccessKeyId, l.svcCtx.Config.OSS.AccessKeySecret)
	stsClient, err := sts.NewClientWithOptions(l.svcCtx.Config.OSS.Region, config, credential)
	if err != nil {
		return nil, fmt.Errorf("创建 STS 客户端失败: %v", err)
	}

	// 设置 STS 客户端的 endpoint
	stsClient.Domain = l.svcCtx.Config.STS.Endpoint

	// 创建 AssumeRole 请求
	assumeRoleReq := sts.CreateAssumeRoleRequest()
	assumeRoleReq.Scheme = "https"
	assumeRoleReq.RoleArn = l.svcCtx.Config.STS.RoleArn
	assumeRoleReq.RoleSessionName = l.svcCtx.Config.STS.RoleSessionName
	assumeRoleReq.DurationSeconds = requests.Integer(strconv.Itoa(l.svcCtx.Config.STS.DurationSeconds))

	// 执行 AssumeRole
	assumeRoleResp, err := stsClient.AssumeRole(assumeRoleReq)
	if err != nil {
		return nil, fmt.Errorf("AssumeRole 失败: %v", err)
	}

	return &StsCredentials{
		AccessKeyId:     assumeRoleResp.Credentials.AccessKeyId,
		AccessKeySecret: assumeRoleResp.Credentials.AccessKeySecret,
		SecurityToken:   assumeRoleResp.Credentials.SecurityToken,
	}, nil
}
