package i18n

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"time"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetI18nPackageVersionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetI18nPackageVersionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetI18nPackageVersionsLogic {
	return &GetI18nPackageVersionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetI18nPackageVersionsLogic) GetI18nPackageVersions() (resp *types.GetI18nPackageVersionsResp, err error) {
	// 获取STS临时授权
	config := sdk.NewConfig()
	credential := credentials.NewAccessKeyCredential(l.svcCtx.Config.OSS.AccessKeyId, l.svcCtx.Config.OSS.AccessKeySecret)
	stsClient, err := sts.NewClientWithOptions(l.svcCtx.Config.OSS.Region, config, credential)
	if err != nil {
		return nil, fmt.Errorf("failed to create STS client: %v", err)
	}

	// 设置STS客户端的endpoint
	stsClient.Domain = l.svcCtx.Config.STS.Endpoint

	assumeRoleReq := sts.CreateAssumeRoleRequest()
	assumeRoleReq.Scheme = "https"
	assumeRoleReq.RoleArn = l.svcCtx.Config.STS.RoleArn
	assumeRoleReq.RoleSessionName = l.svcCtx.Config.STS.RoleSessionName
	assumeRoleReq.DurationSeconds = requests.Integer(strconv.Itoa(l.svcCtx.Config.STS.DurationSeconds))

	assumeRoleResp, err := stsClient.AssumeRole(assumeRoleReq)
	if err != nil {
		return nil, fmt.Errorf("failed to assume role: %v", err)
	}

	// 使用STS凭证创建OSS客户端
	client, err := oss.New(l.svcCtx.Config.OSS.Endpoint,
		assumeRoleResp.Credentials.AccessKeyId,
		assumeRoleResp.Credentials.AccessKeySecret,
		oss.SecurityToken(assumeRoleResp.Credentials.SecurityToken))
	if err != nil {
		return nil, fmt.Errorf("failed to create OSS client: %v", err)
	}

	// 获取 bucket
	bucket, err := client.Bucket(l.svcCtx.Config.OSS.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %v", err)
	}

	// 列出所有 i18n JSON 文件
	var packageVersions []types.I18nPackageVersion
	devices := []string{"android", "ios"}

	for _, device := range devices {
		prefix := fmt.Sprintf("i18n/%s/", device)
		marker := ""

		for {
			lsRes, err := bucket.ListObjects(oss.Prefix(prefix), oss.Marker(marker), oss.MaxKeys(1000))
			if err != nil {
				logx.Errorf("failed to list objects for device %s: %v", device, err)
				continue
			}

			// 正则表达式匹配 i18n/${device}/${version}.json 文件
			jsonRegex := regexp.MustCompile(fmt.Sprintf(`i18n/%s/(.+)\.json$`, device))

			for _, object := range lsRes.Objects {
				if matches := jsonRegex.FindStringSubmatch(object.Key); len(matches) > 1 {
					version := matches[1]

					// 构建下载 URL
					downloadUrl := fmt.Sprintf("https://%s.%s.aliyuncs.com/%s", l.svcCtx.Config.OSS.BucketName,
						l.svcCtx.Config.OSS.Region, object.Key)

					// 尝试获取文件内容以解析 meta 信息
					meta, err := l.getMetaFromJSON(bucket, object.Key)
					if err != nil {
						logx.Errorf("failed to get meta from %s: %v", object.Key, err)
						// 如果解析失败，使用默认值
						meta = &types.I18nPackageMeta{
							Languages: []string{},
						}
					}

					packageVersion := types.I18nPackageVersion{
						Version:            version,
						Device:             device,
						FileName:           fmt.Sprintf("%s.json", version),
						DownloadUrl:        downloadUrl,
						UploadTime:         object.LastModified.Format(time.RFC3339),
						FileSize:           object.Size,
						Languages:          meta.Languages,
						Namespace:          meta.Namespace,
						GeneratedAt:        meta.GeneratedAt,
						IOSAppVersions:     meta.IOSAppVersions,
						AndroidAppVersions: meta.AndroidAppVersions,
					}
					packageVersions = append(packageVersions, packageVersion)
				}
			}

			if !lsRes.IsTruncated {
				break
			}
			marker = lsRes.NextMarker
		}
	}

	// 按上传时间倒序排序
	sort.Slice(packageVersions, func(i, j int) bool {
		return packageVersions[i].UploadTime > packageVersions[j].UploadTime
	})

	return &types.GetI18nPackageVersionsResp{
		Data: packageVersions,
	}, nil
}

// getMetaFromJSON 从JSON文件中获取 meta 信息
func (l *GetI18nPackageVersionsLogic) getMetaFromJSON(bucket *oss.Bucket, objectKey string) (*types.I18nPackageMeta, error) {
	// 获取文件内容
	body, err := bucket.GetObject(objectKey)
	if err != nil {
		return nil, fmt.Errorf("failed to get object %s: %v", objectKey, err)
	}
	defer body.Close()

	// 读取内容
	var content []byte
	buffer := make([]byte, 1024)
	for {
		n, err := body.Read(buffer)
		if n > 0 {
			content = append(content, buffer[:n]...)
		}
		if err != nil {
			break
		}
	}

	// 解析JSON以获取 meta 信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal(content, &jsonData); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	meta := &types.I18nPackageMeta{}

	// 检查是否是新格式（包含 meta 和 data 字段）
	if metaData, hasMeta := jsonData["meta"]; hasMeta {
		if metaMap, ok := metaData.(map[string]interface{}); ok {
			// 解析版本
			if version, hasVersion := metaMap["version"]; hasVersion {
				if versionStr, ok := version.(string); ok {
					meta.Version = versionStr
				}
			}

			// 解析生成时间
			if generatedAt, hasGeneratedAt := metaMap["generatedAt"]; hasGeneratedAt {
				if generatedAtStr, ok := generatedAt.(string); ok {
					meta.GeneratedAt = generatedAtStr
				}
			}

			// 解析命名空间
			if namespace, hasNamespace := metaMap["namespace"]; hasNamespace {
				if namespaceStr, ok := namespace.(string); ok {
					meta.Namespace = namespaceStr
				}
			}

			// 解析语言列表
			if languagesList, hasLanguages := metaMap["languages"]; hasLanguages {
				if langArray, ok := languagesList.([]interface{}); ok {
					for _, lang := range langArray {
						if langStr, ok := lang.(string); ok {
							meta.Languages = append(meta.Languages, langStr)
						}
					}
				}
			}

			// 解析 iOS App 版本
			if iosVersions, hasIOSVersions := metaMap["iosAppVersions"]; hasIOSVersions {
				if iosArray, ok := iosVersions.([]interface{}); ok {
					for _, version := range iosArray {
						if versionStr, ok := version.(string); ok {
							meta.IOSAppVersions = append(meta.IOSAppVersions, versionStr)
						}
					}
				}
			}

			// 解析 Android App 版本
			if androidVersions, hasAndroidVersions := metaMap["androidAppVersions"]; hasAndroidVersions {
				if androidArray, ok := androidVersions.([]interface{}); ok {
					for _, version := range androidArray {
						if versionStr, ok := version.(string); ok {
							meta.AndroidAppVersions = append(meta.AndroidAppVersions, versionStr)
						}
					}
				}
			}
		}
	} else {
		// 兼容旧格式：直接从根级别提取语言代码
		for languageCode := range jsonData {
			meta.Languages = append(meta.Languages, languageCode)
		}
	}

	// 排序语言列表
	sort.Strings(meta.Languages)

	return meta, nil
}
