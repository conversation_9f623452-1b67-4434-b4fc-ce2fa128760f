package i18n

import (
	"context"
	"encoding/json"

	"webapi/internal/svc"
	"webapi/internal/third/xxl"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TriggerI18nExportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTriggerI18nExportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TriggerI18nExportLogic {
	return &TriggerI18nExportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TriggerI18nExportLogic) TriggerI18nExport(req *types.TriggerI18nExportReq) (resp *types.TriggerI18nExportResp, err error) {
	// 参数验证
	if req.Tag == "" {
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "版本标签不能为空",
		}, nil
	}

	if len(req.Languages) == 0 {
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "语言列表不能为空",
		}, nil
	}

	// 设置默认值
	if req.Namespace == "" {
		req.Namespace = "ohsome-app"
	}

	// 构造 xxl-job 参数
	param := map[string]interface{}{
		"tag":                req.Tag,
		"namespace":          req.Namespace,
		"languages":          req.Languages,
		"iosAppVersions":     req.IOSAppVersions,
		"androidAppVersions": req.AndroidAppVersions,
	}

	paramJSON, err := json.Marshal(param)
	if err != nil {
		l.Errorf("序列化任务参数失败: %v", err)
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "参数序列化失败: " + err.Error(),
		}, nil
	}

	// 创建 xxl 服务
	xxlService := xxl.NewXxlService(
		l.svcCtx.Config.XxlJobConf.Username,
		l.svcCtx.Config.XxlJobConf.Password,
		l.svcCtx.Config.XxlJobConf.ServerAddr,
	)

	// 登录获取身份标识
	xxlLoginIdentity, err := xxlService.Login(l.ctx, l.svcCtx.Config.XxlJobConf.Username, l.svcCtx.Config.XxlJobConf.Password)
	if err != nil {
		l.Errorf("xxl-job 登录失败: %v", err)
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "xxl-job 登录失败: " + err.Error(),
		}, nil
	}

	// 获取执行器信息
	executor, err := xxlService.GetExecutorByAppname(l.ctx, l.svcCtx.Config.XxlJobConf.AppName, xxlLoginIdentity)
	if err != nil {
		l.Errorf("获取执行器信息失败: %v", err)
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "获取执行器信息失败: " + err.Error(),
		}, nil
	}

	// 获取任务列表，找到 exportI18nToOSS 任务
	jobs, err := xxlService.ListJobsInJobGroup(l.ctx, executor.Id, xxlLoginIdentity)
	if err != nil {
		l.Errorf("获取任务列表失败: %v", err)
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "获取任务列表失败: " + err.Error(),
		}, nil
	}

	var targetJob *xxl.XxlJobInfo
	for _, job := range jobs {
		if job.ExecutorHandler == "exportI18nToOSS" {
			targetJob = &job
			break
		}
	}

	if targetJob == nil {
		l.Error("未找到 exportI18nToOSS 任务")
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "未找到 exportI18nToOSS 任务",
		}, nil
	}

	// 触发任务执行
	jobId, err := xxlService.TriggerJob(l.ctx, targetJob.Id, string(paramJSON), xxlLoginIdentity)
	if err != nil {
		l.Errorf("触发任务执行失败: %v", err)
		return &types.TriggerI18nExportResp{
			Success: false,
			Message: "触发任务执行失败: " + err.Error(),
		}, nil
	}

	l.Infof("成功触发语言包导出任务，任务ID: %s, 参数: %s", jobId, string(paramJSON))

	return &types.TriggerI18nExportResp{
		Success: true,
		Message: "任务触发成功",
		JobId:   jobId,
	}, nil
}
