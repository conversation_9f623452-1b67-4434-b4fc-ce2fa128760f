package fulfillment_center

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"webapi/internal/repository"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateProductMediaTypeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateProductMediaTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateProductMediaTypeLogic {
	return &UpdateProductMediaTypeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateProductMediaTypeLogic) UpdateProductMediaType(req *types.UpdateProductMediaTypeReq) (resp *types.UpdateProductMediaTypeResp, err error) {
	// 1. 解析 ID
	id, err := strconv.ParseInt(req.Id, 10, 64)
	if err != nil {
		return &types.UpdateProductMediaTypeResp{
			Success: false,
			Message: "无效的ID格式",
		}, nil
	}

	// 2. 查询 product_media by id
	repo := repository.NewProductMediaRepository()
	media, err := repo.GetByID(l.ctx, id)
	if err != nil {
		return &types.UpdateProductMediaTypeResp{
			Success: false,
			Message: fmt.Sprintf("查询产品媒体失败: %v", err),
		}, nil
	}

	// check if media_url contains "/ls/"
	if !strings.Contains(media.MediaURL, "/ls/") {
		return &types.UpdateProductMediaTypeResp{
			Success: false,
			Message: "不是 LS 的图片，无需处理",
		}, nil
	}

	// 3. 检查是否已经处理过
	if media.MediaType == "scm_white_image" || media.MediaType == "scm_gallery_image" {
		return &types.UpdateProductMediaTypeResp{
			Success: true,
			Message: "该媒体类型已经处理过，无需重复处理",
		}, nil
	}

	// 4. 更新 media_type
	var newMediaType string
	switch media.MediaType {
	case "gallery_image":
		newMediaType = "scm_gallery_image"
	case "white_image":
		newMediaType = "scm_white_image"
	default:
		return &types.UpdateProductMediaTypeResp{
			Success: false,
			Message: fmt.Sprintf("不支持的媒体类型: %s", media.MediaType),
		}, nil
	}

	media.MediaType = newMediaType

	// 5. 如果 media_hash 为空，计算 hash
	if media.MediaHash == "" {
		hash, err := l.calculateMediaHash(media.MediaURL)
		if err != nil {
			l.Logger.Errorf("计算媒体hash失败: %v", err)
			// hash 计算失败不影响主流程，继续更新 media_type
		} else {
			media.MediaHash = hash
		}
	}
	
	media.UpdatedAt = time.Now()
	// 6. 更新数据库
	err = repo.Update(l.ctx, media)
	if err != nil {
		return &types.UpdateProductMediaTypeResp{
			Success: false,
			Message: fmt.Sprintf("更新产品媒体失败: %v", err),
		}, nil
	}

	return &types.UpdateProductMediaTypeResp{
		Success: true,
		Message: "更新成功",
	}, nil
}

// calculateMediaHash 计算媒体文件的 MD5 hash
func (l *UpdateProductMediaTypeLogic) calculateMediaHash(mediaURL string) (string, error) {
	if mediaURL == "" {
		return "", fmt.Errorf("媒体URL为空")
	}

	// 下载文件
	data, err := l.downloadFile(mediaURL)
	if err != nil {
		return "", fmt.Errorf("下载文件失败: %v", err)
	}

	// 计算 MD5 hash
	return l.calculateMD5(data), nil
}

// downloadFile 从URL下载文件
func (l *UpdateProductMediaTypeLogic) downloadFile(url string) ([]byte, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP status: %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// calculateMD5 计算二进制数据的 MD5 哈希值
func (l *UpdateProductMediaTypeLogic) calculateMD5(data []byte) string {
	// 创建 MD5 哈希器
	hasher := md5.New()

	// 写入数据
	hasher.Write(data)

	// 计算哈希值并转换为16进制字符串
	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash)
}
