package release

import (
	"context"
	"encoding/json"
	"fmt"

	"webapi/internal/common/utils"
	"webapi/internal/constants"
	"webapi/internal/repository"
	"webapi/internal/svc"
	"webapi/internal/third/lark"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateFeishuApprovalForReleasePlanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateFeishuApprovalForReleasePlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateFeishuApprovalForReleasePlanLogic {
	return &CreateFeishuApprovalForReleasePlanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateFeishuApprovalForReleasePlanLogic) CreateFeishuApprovalForReleasePlan(req *types.PlanIdReq) (resp *types.BaseResponse, err error) {
	// 1. 检查发布计划是否存在
	releasePlan, err := l.svcCtx.ReleasePlanRepo.GetOne(l.ctx, req.PlanId)
	if err != nil {
		l.Errorf("获取发布计划失败: %v", err)
		return &types.BaseResponse{Code: 400, Msg: "发布计划不存在"}, nil
	}

	// 2. 检查系统内审批是否已完成
	if releasePlan.ApprovalInstanceId == 0 {
		l.Errorf("发布计划 %s 尚未创建系统内审批", req.PlanId)
		return &types.BaseResponse{Code: 400, Msg: "请先创建系统内审批流程"}, nil
	}

	// 检查系统内审批状态
	approvalInstance, err := l.svcCtx.ApprovalInstanceRepo.GetOne(l.ctx, releasePlan.ApprovalInstanceId)
	if err != nil {
		l.Errorf("获取审批实例失败: %v", err)
		return &types.BaseResponse{Code: 400, Msg: "审批实例不存在"}, nil
	}

	if approvalInstance.Status != constants.InstanceStatusCompleted {
		l.Errorf("发布计划 %s 的系统内审批尚未完成，当前状态: %s", req.PlanId, approvalInstance.Status)
		return &types.BaseResponse{Code: 400, Msg: "系统内审批流程尚未完成，无法创建飞书审批"}, nil
	}

	// 3. 检查是否已经创建过飞书审批
	if releasePlan.FeishuApprovalInstanceId != "" {
		l.Infof("发布计划 %s 已存在飞书审批实例: %s", req.PlanId, releasePlan.FeishuApprovalInstanceId)
		return &types.BaseResponse{Code: 400, Msg: "该发布计划已创建飞书审批"}, nil
	}

	// 4. 创建飞书审批实例
	feishuInstanceId, err := l.createFeishuApprovalInstance(releasePlan)
	if err != nil {
		l.Errorf("创建飞书审批实例失败: %v", err)
		return &types.BaseResponse{Code: 500, Msg: fmt.Sprintf("创建飞书审批失败: %v", err)}, nil
	}

	// 5. 更新发布计划的飞书审批实例ID
	releasePlan.FeishuApprovalInstanceId = feishuInstanceId
	err = l.svcCtx.ReleasePlanRepo.Update(l.ctx, req.PlanId, releasePlan)
	if err != nil {
		l.Errorf("更新发布计划飞书审批实例ID失败: %v", err)
		return &types.BaseResponse{Code: 500, Msg: "更新发布计划失败"}, nil
	}

	l.Infof("成功为发布计划 %s 创建飞书审批，实例ID: %s", req.PlanId, feishuInstanceId)
	return &types.BaseResponse{Code: 0, Msg: "创建飞书审批成功"}, nil
}

// createFeishuApprovalInstance 创建飞书审批实例
func (l *CreateFeishuApprovalForReleasePlanLogic) createFeishuApprovalInstance(releasePlan *repository.ReleasePlan) (string, error) {
	// 1. 获取创建人的飞书用户ID
	userEmail := releasePlan.CreatedBy
	if userEmail == "" {
		// 如果发布计划没有创建人信息，尝试从上下文获取
		userEmail = utils.GetEmailFromCtx(l.ctx)
		if userEmail == "" {
			return "", fmt.Errorf("无法获取用户邮箱")
		}
	}

	userId, err := l.svcCtx.LarkSvc.GetUserIDByEmail(l.ctx, userEmail)
	if err != nil {
		return "", fmt.Errorf("获取用户飞书ID失败: %w", err)
	}

	// 2. 获取固定用户（<EMAIL>）的飞书用户ID
	duanwuxueUserId, err := l.svcCtx.LarkSvc.GetUserIDByEmail(l.ctx, "<EMAIL>")
	if err != nil {
		return "", fmt.Errorf("获取duanwuxue用户飞书ID失败: %w", err)
	}

	// 3. 获取应用任务详情
	applicationTasks, err := l.getApplicationTaskDetails(releasePlan.Id)
	if err != nil {
		return "", fmt.Errorf("获取应用任务详情失败: %w", err)
	}

	// 4. 获取所有相关URL
	allUrls := l.getAllUrls(releasePlan)

	// 5. 构建审批表单数据
	form := lark.ReleasePlanApprovalForm{
		PlanName:         releasePlan.Name,
		Description:      releasePlan.Description,
		AllUrls:          allUrls,
		ApplicationTasks: applicationTasks,
		CreatedBy:        userEmail,
	}

	// 根据环境选择对应的表单构建函数
	var formData string
	if l.isProductionEnvironment() {
		formData = lark.BuildReleasePlanApprovalFormForProd(form, duanwuxueUserId)
	} else {
		formData = lark.BuildReleasePlanApprovalFormForTest(form, duanwuxueUserId)
	}

	// 6. 创建飞书审批实例
	approvalCode := l.svcCtx.Config.FeiShu.ReleaseApprovalCode
	createReq := &lark.CreateApprovalInstanceRequest{
		ApprovalCode: approvalCode,
		UserId:       userId,
		Form:         formData,
	}

	instanceId, err := l.svcCtx.LarkSvc.CreateApprovalInstance(l.ctx, createReq)
	if err != nil {
		return "", fmt.Errorf("创建飞书审批实例失败: %w", err)
	}

	l.Infof("成功创建飞书审批实例，发布计划: %s, 实例ID: %s, 用户: %s",
		releasePlan.Id, instanceId, userEmail)

	return instanceId, nil
}

// getApplicationTaskDetails 获取应用任务详情
func (l *CreateFeishuApprovalForReleasePlanLogic) getApplicationTaskDetails(planId string) ([]lark.ApplicationTaskDetail, error) {
	// 获取发布计划的应用任务
	tasks, err := l.svcCtx.ReleaseApplicationTaskRepo.GetByPlanId(l.ctx, planId)
	if err != nil {
		return nil, fmt.Errorf("获取应用任务失败: %w", err)
	}

	var taskDetails []lark.ApplicationTaskDetail
	for _, task := range tasks {
		// 获取应用信息
		app, err := l.svcCtx.ApplicationRepo.GetOne(l.ctx, task.ApplicationId)
		if err != nil {
			l.Errorf("获取应用信息失败，应用ID: %d, 错误: %v", task.ApplicationId, err)
			continue
		}

		// 构建服务名称：jenkins_job_full_path-(name)
		serviceName := fmt.Sprintf("%s-(%s)", app.JenkinsJobFullPath, app.Name)

		taskDetails = append(taskDetails, lark.ApplicationTaskDetail{
			ServiceName: serviceName,
			GitBranch:   task.GitBranch,
		})
	}

	return taskDetails, nil
}

// getAllUrls 获取所有相关URL
func (l *CreateFeishuApprovalForReleasePlanLogic) getAllUrls(releasePlan *repository.ReleasePlan) []string {
	var allUrls []string

	// 添加发布手册URLs
	if releasePlan.ReleaseManualUrls != "" {
		var manualUrls []string
		if err := json.Unmarshal([]byte(releasePlan.ReleaseManualUrls), &manualUrls); err == nil {
			allUrls = append(allUrls, manualUrls...)
		}
	}

	// 添加其他相关URL
	if releasePlan.MergeRequestUrls != "" {
		var mergeRequestUrls []string
		if err := json.Unmarshal([]byte(releasePlan.MergeRequestUrls), &mergeRequestUrls); err == nil {
			allUrls = append(allUrls, mergeRequestUrls...)
		}
	}

	if releasePlan.PrdUrls != "" {
		var prdUrls []string
		if err := json.Unmarshal([]byte(releasePlan.PrdUrls), &prdUrls); err == nil {
			allUrls = append(allUrls, prdUrls...)
		}
	}

	if releasePlan.TechDesignUrls != "" {
		var techDesignUrls []string
		if err := json.Unmarshal([]byte(releasePlan.TechDesignUrls), &techDesignUrls); err == nil {
			allUrls = append(allUrls, techDesignUrls...)
		}
	}

	return allUrls
}

// isProductionEnvironment 判断是否为生产环境
func (l *CreateFeishuApprovalForReleasePlanLogic) isProductionEnvironment() bool {
	// 根据配置中的 AppId 来判断环境
	// 生产环境: cli_a7d56bbafb7e100b
	// 测试环境: cli_a8fa0c729a655013
	return l.svcCtx.Config.FeiShu.AppId == "cli_a7d56bbafb7e100b"
}
