{{define "gen_enhanced"}}
// Package query Code generated by GORM Gen.
package {{.SiteName}}

import (
	"{{.PackageName}}/query"
	"context"
)

type {{.ModelName}}Do struct {
	ctx context.Context
	*query.Query
}

func Get{{.ModelName}}Enhanced(ctxIn context.Context) *{{.ModelName}}Do {
	return &{{.ModelName}}Do{
		ctx:   ctxIn,
		Query: DefaultQ(ctxIn),
	}
}

// 自定义方法示例
// func (d *{{.ModelName}}Do) GetMaxPriority() (m int64, err error) {
// 	 err = d.{{.ModelName}}.WithContext(d.ctx).
//			Select(d.{{.ModelName}}.Priority.Max()).
//			Scan(&m)
//	 return
// }

// Logic 层调用示例
// 1、默认 GORM Gen 生成函数：
// 		q := dal.DefaultQ(ctxIn)
// 		data, err := q.{{.ModelName}}.Where(q.{{.ModelName}}.ID.Eq(1)).First()
//
// 2、自定义函数调用
// 		q := dal.Get{{.ModelName}}Enhanced(ctx)
// 		data, err := q.GetMaxPriority()

{{end}}
