{{define "gen_dal"}}
package dal

import (
	"{{.ProjectName}}/query"
	"context"
	"git.blueorigin.work/e-commerce/locale-hub-common/datasource"
	"git.blueorigin.work/e-commerce/locale-hub-common/locale"
	"sync"
)

var m = struct {
	mu sync.RWMutex
	Q  map[string]*query.Query
}{
	Q: make(map[string]*query.Query),
}

func DefaultQ(ctx context.Context) *query.Query {
	region := locale.LocaleFromCtxOrDefault(ctx).CountryCode
	m.mu.RLock()
	q, ok := m.Q[region]
	m.mu.RUnlock()
	if ok {
		return q
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	m.Q[region] = query.Use(datasource.GetDsCtxOrDefault(ctx))
	return m.Q[region]
}
{{end}}
