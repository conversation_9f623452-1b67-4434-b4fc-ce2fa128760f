package ops

import (
	"context"
	"testing"

	"webapi/internal/config"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/stretchr/testify/assert"
)

func TestGenerateGormDalAndPushToGitlab(t *testing.T) {
	// Test with missing GitLab token
	svcCtxNoToken := &svc.ServiceContext{
		Config: config.Config{
			GitLab: struct {
				Host  string
				Token string
			}{
				Host:  "https://git.blueorigin.work",
				Token: "", // Empty token
			},
		},
	}

	logicNoToken := NewGenerateGormDalAndPushToGitlabLogic(context.Background(), svcCtxNoToken)
	req := &types.GenerateGormDalAndPushToGitlabReq{
		DatabaseNames: []string{"ops"},
	}

	resp, err := logicNoToken.GenerateGormDalAndPushToGitlab(req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.False(t, resp.Success)
	assert.Equal(t, "GitLab token not configured", resp.Message)
}

func TestGenerateGormDalAndPushToGitlab_WithDatabases(t *testing.T) {
	// Skip this test in CI/CD or when GitLab is not accessible
	t.Skip("Skipping integration test - requires GitLab access and valid token")

	cfg := config.Config{
		GitLab: struct {
			Host  string
			Token string
		}{
			Host:  "https://git.blueorigin.work",
			Token: "your-actual-token-here", // Replace with actual token for integration testing
		},
	}

	svcCtx := &svc.ServiceContext{
		Config: cfg,
	}

	logic := NewGenerateGormDalAndPushToGitlabLogic(context.Background(), svcCtx)

	// Test with actual site names
	req := &types.GenerateGormDalAndPushToGitlabReq{
		DatabaseNames: []string{"ops"},
	}

	resp, err := logic.GenerateGormDalAndPushToGitlab(req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	// Note: This might fail if GitLab repo doesn't exist or token is invalid
	// In a real test environment, you'd want to mock the git operations
}
