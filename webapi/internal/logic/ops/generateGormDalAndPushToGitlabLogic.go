package ops

import (
	"bytes"
	"context"
	"embed"
	"encoding/json"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"text/template"
	"time"
	"unicode"

	"webapi/datasource"
	"webapi/internal/svc"
	webapiTypes "webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"
	"gorm.io/gen"
	"gorm.io/gorm"

	_ "embed"
)

//go:embed templates/*
var templateFS embed.FS

const (
	RepoDir   = "/tmp/gorm-generated-dal"
	GitLabAPI = "https://git.blueorigin.work/api/v4"
	ProjectID = "e-commerce/gorm-generated-dal" // 项目路径
)

// GitLab API 相关结构体
type GitLabFile struct {
	FileName      string `json:"file_name"`
	FilePath      string `json:"file_path"`
	Size          int    `json:"size"`
	Encoding      string `json:"encoding"`
	Content       string `json:"content"`
	ContentSha256 string `json:"content_sha256"`
	Ref           string `json:"ref"`
	BlobID        string `json:"blob_id"`
	CommitID      string `json:"commit_id"`
	LastCommitID  string `json:"last_commit_id"`
}

type GitLabCommitAction struct {
	Action   string `json:"action"` // create, delete, move, update
	FilePath string `json:"file_path"`
	Content  string `json:"content,omitempty"`
}

type GitLabCommitRequest struct {
	Branch        string               `json:"branch"`
	CommitMessage string               `json:"commit_message"`
	Actions       []GitLabCommitAction `json:"actions"`
}

type GenerateGormDalAndPushToGitlabLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateGormDalAndPushToGitlabLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateGormDalAndPushToGitlabLogic {
	return &GenerateGormDalAndPushToGitlabLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateGormDalAndPushToGitlabLogic) GenerateGormDalAndPushToGitlab(req *webapiTypes.GenerateGormDalAndPushToGitlabReq) (resp *webapiTypes.GenerateGormDalAndPushToGitlabResp, err error) {
	token := l.svcCtx.Config.GitLab.Token
	if token == "" {
		return &webapiTypes.GenerateGormDalAndPushToGitlabResp{
			Success: false,
			Message: "GitLab token not configured",
		}, nil
	}

	// 1. Clone or Pull repository
	if err := l.cloneOrPullRepo(token); err != nil {
		l.Errorf("Failed to clone or update repository: %v", err)
		return &webapiTypes.GenerateGormDalAndPushToGitlabResp{
			Success: false,
			Message: fmt.Sprintf("Failed to clone or update repository: %v", err),
		}, nil
	}

	// 2. Generate GORM DAL code for specified sites
	for _, siteName := range req.DatabaseNames {
		outputDir := filepath.Join(RepoDir, siteName, "dal")
		if err := l.generateGormDAL(siteName, outputDir); err != nil {
			l.Errorf("Failed to generate DAL for site %s: %v", siteName, err)
			continue
		}
		l.Infof("✅ Generated DAL for site %s to %s", siteName, outputDir)
	}

	// 3. Push to GitLab using Git commands
	if err := l.pushToGitLabWithGit(token); err != nil {
		l.Errorf("Failed to push to GitLab: %v", err)
		return &webapiTypes.GenerateGormDalAndPushToGitlabResp{
			Success: false,
			Message: fmt.Sprintf("Failed to push to GitLab: %v", err),
		}, nil
	}

	l.Info("🎉 All DAL code has been successfully generated and pushed to remote repository!")
	return &webapiTypes.GenerateGormDalAndPushToGitlabResp{
		Success: true,
		Message: "DAL code generated and pushed successfully",
	}, nil
}

// cloneOrPullRepo: 删除目录并重新 clone 仓库
func (l *GenerateGormDalAndPushToGitlabLogic) cloneOrPullRepo(token string) error {
	// 删除现有目录（如果存在）
	if _, err := os.Stat(RepoDir); err == nil {
		l.Infof("🗑️ Removing existing directory: %s", RepoDir)
		if err := os.RemoveAll(RepoDir); err != nil {
			return fmt.Errorf("failed to remove existing directory: %v", err)
		}
	}

	// 构建 Git clone 命令
	repoURL := fmt.Sprintf("https://oauth2:%<EMAIL>/%s.git", token, ProjectID)

	l.Infof("📥 Cloning repository to: %s", RepoDir)

	// 执行 git clone 命令
	cmd := exec.Command("git", "clone", repoURL, RepoDir)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to clone repository: %v", err)
	}

	l.Infof("✅ Repository cloned successfully: %s", RepoDir)
	return nil
}

// generateGormDAL: 参照 scripts/gormgen 实现 GORM DAL 代码生成
func (l *GenerateGormDalAndPushToGitlabLogic) generateGormDAL(siteName, outputDir string) error {
	l.Infof("🔧 Generating GORM DAL for site: %s", siteName)

	// 创建输出目录
	queryDir := filepath.Join(outputDir, "query")
	modelDir := filepath.Join(outputDir, "model")

	if err := os.MkdirAll(queryDir, 0755); err != nil {
		return fmt.Errorf("failed to create query directory: %v", err)
	}
	if err := os.MkdirAll(modelDir, 0755); err != nil {
		return fmt.Errorf("failed to create model directory: %v", err)
	}

	// 获取指定站点的数据库连接
	db := datasource.GetDsSiteDefault(siteName)
	if db == nil {
		return fmt.Errorf("failed to get database connection for site: %s", siteName)
	}

	l.Infof("Generating DAL for site: %s", siteName)

	// 配置 GORM Gen
	g := gen.NewGenerator(gen.Config{
		OutPath:          queryDir,
		ModelPkgPath:     modelDir,
		FieldWithTypeTag: true,
		Mode:             gen.WithDefaultQuery | gen.WithQueryInterface,
	})

	g.UseDB(db)

	// 获取数据库中的所有表
	tables, err := l.getTables(db)
	if err != nil {
		return fmt.Errorf("failed to get tables: %v", err)
	}

	l.Infof("Found %d tables for site %s", len(tables), siteName)

	// 为每个表生成模型
	for _, tableName := range tables {
		g.ApplyBasic(g.GenerateModel(tableName))
	}

	// 执行生成
	g.Execute()

	// 并发执行后续生成步骤
	if err := l.generatePostProcessingConcurrently(siteName, outputDir, modelDir); err != nil {
		return fmt.Errorf("failed to generate post-processing: %v", err)
	}

	return nil
}

// getTables: 获取当前数据库连接中的所有表
func (l *GenerateGormDalAndPushToGitlabLogic) getTables(db *gorm.DB) ([]string, error) {
	// 直接查询当前数据库的所有表，不需要指定数据库名称
	tableQuery := `SELECT table_name FROM information_schema.tables
		WHERE table_schema = DATABASE() AND table_name NOT LIKE '%.%';`

	var tables []string
	if err := db.Raw(tableQuery).Scan(&tables).Error; err != nil {
		return nil, fmt.Errorf("failed to query table names: %v", err)
	}

	return tables, nil
}

// generatePostProcessingConcurrently: 并发执行后续生成步骤
func (l *GenerateGormDalAndPushToGitlabLogic) generatePostProcessingConcurrently(siteName, outputDir, modelDir string) error {
	l.Info("🚀 Starting concurrent post-processing...")

	// 使用 errgroup 进行并发处理
	g := &errgroup.Group{}

	// 任务1: 生成切片别名
	g.Go(func() error {
		l.Info("📝 Generating slice aliases...")
		if err := l.generateSliceAliases(modelDir); err != nil {
			l.Errorf("Failed to generate slice aliases: %v", err)
			return err
		}
		l.Info("✅ Slice aliases generated")
		return nil
	})

	// 任务2: 生成 dal.gen.go 文件
	g.Go(func() error {
		l.Info("🔧 Generating dal file...")
		if err := l.generateDalFile(siteName, outputDir); err != nil {
			l.Errorf("Failed to generate dal file: %v", err)
			return err
		}
		l.Info("✅ Dal file generated")
		return nil
	})

	// 任务3: 生成增强查询文件
	g.Go(func() error {
		l.Info("⚡ Generating enhanced queries...")
		if err := l.generateEnhancedQuery(siteName, outputDir); err != nil {
			l.Errorf("Failed to generate enhanced query: %v", err)
			return err
		}
		l.Info("✅ Enhanced queries generated")
		return nil
	})

	// 等待前3个任务完成
	if err := g.Wait(); err != nil {
		return fmt.Errorf("concurrent post-processing failed: %v", err)
	}

	// 任务4: 转换查询文件（必须在其他任务完成后执行）
	l.Info("🔄 Converting query files to use slice aliases...")
	if err := l.convertQueryFilesToUseSliceAlias(outputDir); err != nil {
		l.Errorf("Failed to convert query files: %v", err)
		return fmt.Errorf("failed to convert query files: %v", err)
	}
	l.Info("✅ Query files converted")

	l.Info("🎉 Concurrent post-processing completed!")
	return nil
}

// generateSliceAliases: 生成模型切片别名
func (l *GenerateGormDalAndPushToGitlabLogic) generateSliceAliases(modelDir string) error {
	// 查找所有生成的模型文件
	modelFiles, err := l.findGenModelGoFiles(modelDir)
	if err != nil {
		return fmt.Errorf("failed to find model files: %v", err)
	}

	l.Infof("Found %d model files for slice alias generation", len(modelFiles))

	tpl := fmt.Sprintf(`
package %s
type {{.ModelName}}Slice []*{{.ModelName}}
`, filepath.Base(modelDir))

	t, err := template.New("model_slice").Parse(tpl)
	if err != nil {
		return err
	}

	// 为每个模型文件生成切片别名
	for _, modelFile := range modelFiles {
		modelName := l.extractModelNameFromFile(filepath.Base(modelFile))
		if modelName == "" {
			continue
		}

		sliceFileName := "slice_" + l.toSnakeCase(modelName) + ".go"
		slicePath := filepath.Join(modelDir, sliceFileName)

		// 如果文件已经存在，跳过
		if exists, _ := l.pathExists(slicePath); exists {
			continue
		}

		// 创建切片别名文件
		f, err := os.Create(slicePath)
		if err != nil {
			l.Errorf("Failed to create slice file %s: %v", slicePath, err)
			continue
		}

		err = t.Execute(f, map[string]interface{}{
			"ModelName": modelName,
		})
		if err != nil {
			f.Close()
			l.Errorf("Failed to execute template for %s: %v", slicePath, err)
			continue
		}
		f.Close()
		l.Infof("Generated slice alias file: %s", slicePath)
	}

	return nil
}

// generateDalFile: 生成 dal.gen.go 文件
func (l *GenerateGormDalAndPushToGitlabLogic) generateDalFile(siteName, outputDir string) error {
	// 读取嵌入的模板文件
	tplData, err := templateFS.ReadFile("templates/gen_dal.tpl")
	if err != nil {
		return fmt.Errorf("failed to read dal template: %v", err)
	}

	// 解析模板
	t, err := template.New("gen_dal").Parse(string(tplData))
	if err != nil {
		return fmt.Errorf("failed to parse dal template: %v", err)
	}

	// 创建输出文件
	dalFile := filepath.Join(outputDir, "dal.gen.go")
	f, err := os.Create(dalFile)
	if err != nil {
		return err
	}
	defer f.Close()

	// 执行模板
	return t.ExecuteTemplate(f, "gen_dal", map[string]interface{}{
		"ProjectName": siteName,
	})
}

// ModelInfo 模型信息结构体
type ModelInfo struct {
	ModelName      string
	PackageName    string
	QueryInterface string
}

// generateEnhancedQuery: 生成增强查询文件
func (l *GenerateGormDalAndPushToGitlabLogic) generateEnhancedQuery(siteName, outputDir string) error {
	queryDir := filepath.Join(outputDir, "query")

	// 查找所有生成的 query 文件
	genFiles, err := l.findGenQueryGoFiles(queryDir)
	if err != nil {
		return fmt.Errorf("failed to find query files: %v", err)
	}

	l.Infof("Found %d query files for enhanced generation", len(genFiles))

	// 使用 errgroup 并发生成增强文件
	g := &errgroup.Group{}
	g.SetLimit(5) // 限制并发数量

	for _, filename := range genFiles {
		filename := filename // 避免闭包问题
		g.Go(func() error {
			if err := l.generateEnhancedFileFromQuery(filename, siteName, outputDir); err != nil {
				l.Errorf("Failed to generate enhanced file for %s: %v", filename, err)
				return err
			}
			l.Infof("✅ Enhanced file generated for: %s", filepath.Base(filename))
			return nil
		})
	}

	return g.Wait()
}

// findGenQueryGoFiles 递归查找目录下所有 *.gen.go 文件
func (l *GenerateGormDalAndPushToGitlabLogic) findGenQueryGoFiles(dir string) ([]string, error) {
	var genFiles []string

	// 使用 filepath.Walk 递归遍历目录
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件是否以 .gen.go 结尾，但排除 gen.go 文件本身
		if strings.HasSuffix(path, ".gen.go") && !strings.HasSuffix(path, "/gen.go") {
			genFiles = append(genFiles, path)
		}

		return nil
	})

	return genFiles, err
}

// findGenModelGoFiles 递归查找模型目录下所有 *.gen.go 文件
func (l *GenerateGormDalAndPushToGitlabLogic) findGenModelGoFiles(dir string) ([]string, error) {
	var genFiles []string

	// 使用 filepath.Walk 递归遍历目录
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件是否以 .gen.go 结尾
		if strings.HasSuffix(path, ".gen.go") {
			genFiles = append(genFiles, path)
		}

		return nil
	})

	return genFiles, err
}

// pushToGitLabWithGit: 使用 Git 命令推送文件到 GitLab
func (l *GenerateGormDalAndPushToGitlabLogic) pushToGitLabWithGit(token string) error {
	// 进入工作目录
	originalDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get current directory: %v", err)
	}

	// 切换到仓库目录
	if err := os.Chdir(RepoDir); err != nil {
		return fmt.Errorf("failed to change to repo directory: %v", err)
	}

	// 确保最后切换回原目录
	defer func() {
		os.Chdir(originalDir)
	}()

	// 配置 Git 用户信息
	if err := l.configureGitUser(); err != nil {
		return fmt.Errorf("failed to configure git user: %v", err)
	}

	// 添加所有文件到 Git
	l.Infof("📝 Adding all files to Git...")
	if err := l.runGitCommand("add", "."); err != nil {
		return fmt.Errorf("failed to add files: %v", err)
	}

	// 检查是否有变更
	hasChanges, err := l.checkForChanges()
	if err != nil {
		return fmt.Errorf("failed to check for changes: %v", err)
	}

	if !hasChanges {
		l.Info("🟢 No changes to commit")
		return nil
	}

	// 提交变更
	commitMessage := fmt.Sprintf("Auto-generated GORM DAL code - %s", time.Now().Format("2006-01-02 15:04:05"))
	l.Infof("� Committing changes with message: %s", commitMessage)
	if err := l.runGitCommand("commit", "-m", commitMessage); err != nil {
		return fmt.Errorf("failed to commit changes: %v", err)
	}

	// 推送到远程仓库
	l.Infof("📤 Pushing to GitLab...")
	if err := l.runGitCommand("push", "origin", "main"); err != nil {
		return fmt.Errorf("failed to push to GitLab: %v", err)
	}

	l.Info("✅ Successfully pushed to GitLab")
	return nil
}

// pathExists: 检查路径是否存在
func (l *GenerateGormDalAndPushToGitlabLogic) pathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// toSnakeCase: 将字符串转换为下划线命名
func (l *GenerateGormDalAndPushToGitlabLogic) toSnakeCase(str string) string {
	var result []rune
	var prevChar rune

	for i, char := range str {
		// 处理非字母数字字符（转换为下划线）
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) {
			if len(result) > 0 && result[len(result)-1] != '_' {
				result = append(result, '_')
			}
			prevChar = char
			continue
		}

		// 当前字符是大写字母
		if unicode.IsUpper(char) {
			// 在前一个大写字母后或数字后插入下划线（处理驼峰和帕斯卡）
			if i > 0 && (unicode.IsLower(prevChar) || (i > 0 && unicode.IsDigit(prevChar))) {
				result = append(result, '_')
			}
			result = append(result, unicode.ToLower(char))
			prevChar = char
			continue
		}

		// 当前是数字，前一个是字母
		if unicode.IsDigit(char) && i > 0 && unicode.IsLetter(prevChar) {
			result = append(result, '_')
		}

		result = append(result, char)
		prevChar = char
	}

	snakeStr := string(result)

	// 清理可能的多余下划线
	snakeStr = strings.ReplaceAll(snakeStr, "__", "_")
	snakeStr = strings.Trim(snakeStr, "_")

	return strings.ToLower(snakeStr)
}

// generateEnhancedFileFromQuery: 从 query 文件生成增强查询文件
func (l *GenerateGormDalAndPushToGitlabLogic) generateEnhancedFileFromQuery(filename, siteName, outputDir string) error {
	// 由于缺少 go/ast 等包的导入，这里先实现一个简化版本
	// 从文件名推断模型名称
	baseName := filepath.Base(filename)
	modelName := l.extractModelNameFromFile(baseName)

	if modelName == "" {
		return nil // 跳过无法识别的文件
	}

	// 生成增强查询文件
	return l.generateEnhancedFile(ModelInfo{
		ModelName:      modelName,
		PackageName:    "dal",
		QueryInterface: "I" + modelName + "Do",
	}, siteName, outputDir)
}

// extractModelNameFromFile: 从文件名提取模型名称
func (l *GenerateGormDalAndPushToGitlabLogic) extractModelNameFromFile(filename string) string {
	// 检查是否是 .gen.go 文件
	if !strings.HasSuffix(filename, ".gen.go") {
		return ""
	}

	// 移除 .gen.go 后缀
	name := strings.TrimSuffix(filename, ".gen.go")

	// 跳过特殊文件
	if name == "gen" {
		return ""
	}

	// 转换为 PascalCase
	return l.toPascalCase(name)
}

// toPascalCase: 转换为 PascalCase
func (l *GenerateGormDalAndPushToGitlabLogic) toPascalCase(str string) string {
	words := strings.Split(str, "_")
	var result strings.Builder

	for _, word := range words {
		if len(word) > 0 {
			result.WriteString(strings.ToUpper(string(word[0])))
			if len(word) > 1 {
				result.WriteString(strings.ToLower(word[1:]))
			}
		}
	}

	return result.String()
}

// generateEnhancedFile: 生成增强查询文件
func (l *GenerateGormDalAndPushToGitlabLogic) generateEnhancedFile(info ModelInfo, siteName, outputDir string) error {
	// 读取嵌入的模板文件
	tplData, err := templateFS.ReadFile("templates/gen_enhanced.tpl")
	if err != nil {
		return fmt.Errorf("failed to read enhanced template: %v", err)
	}

	// 解析模板，使用 fmt.Sprintf 替换 %s 占位符
	templateContent := fmt.Sprintf(string(tplData), siteName)

	// 创建模板函数映射
	funcMap := template.FuncMap{
		"toSmallCamelCase": l.toSmallCamelCase,
	}

	t, err := template.New("gen_enhanced").Funcs(funcMap).Parse(templateContent)
	if err != nil {
		return fmt.Errorf("failed to parse enhanced template: %v", err)
	}

	// 生成文件名
	toSmallCamel := strings.ToLower(string(info.ModelName[0])) + info.ModelName[1:]
	outputPath := filepath.Join(outputDir, toSmallCamel+".enhanced.go")

	// 检查文件是否已存在
	if exists, _ := l.pathExists(outputPath); exists {
		return nil // 跳过已存在的文件
	}

	// 创建文件
	f, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer f.Close()

	// 执行模板
	return t.ExecuteTemplate(f, "gen_enhanced", info)
}

// toSmallCamelCase: 转换为小驼峰命名
func (l *GenerateGormDalAndPushToGitlabLogic) toSmallCamelCase(str string) string {
	if str == "" {
		return ""
	}

	// 如果包含下划线，先转换为 PascalCase 再首字母小写
	if strings.Contains(str, "_") {
		pascalCase := l.toPascalCase(str)
		if pascalCase == "" {
			return ""
		}
		return strings.ToLower(string(pascalCase[0])) + pascalCase[1:]
	}

	// 如果是 PascalCase，转换为小驼峰
	if unicode.IsUpper(rune(str[0])) {
		return strings.ToLower(string(str[0])) + str[1:]
	}

	// 已经是小驼峰或小写，直接返回
	return str
}

// convertQueryFilesToUseSliceAlias: 转换查询文件以使用切片别名
func (l *GenerateGormDalAndPushToGitlabLogic) convertQueryFilesToUseSliceAlias(outputDir string) error {
	queryDir := filepath.Join(outputDir, "query")

	// 查找所有生成的 query 文件
	genFiles, err := l.findGenQueryGoFiles(queryDir)
	if err != nil {
		return fmt.Errorf("failed to find query files: %v", err)
	}

	l.Infof("Converting %d query files to use slice aliases", len(genFiles))

	// 使用 errgroup 并发处理查询文件
	g := &errgroup.Group{}
	g.SetLimit(10) // AST 处理相对轻量，可以更高并发

	var mu sync.Mutex
	var processedCount int

	for _, filename := range genFiles {
		filename := filename // 避免闭包问题
		g.Go(func() error {
			if err := l.ProcessQueryFile(filename); err != nil {
				l.Errorf("Failed to process query file %s: %v", filename, err)
				return err
			}

			mu.Lock()
			processedCount++
			l.Infof("✅ Processed query file (%d/%d): %s", processedCount, len(genFiles), filepath.Base(filename))
			mu.Unlock()

			return nil
		})
	}

	return g.Wait()
}

// ProcessQueryFile: 处理单个查询文件 (直接移植自 scripts/gormgen/convert/convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) ProcessQueryFile(filename string) error {
	// 完全移植 processFile 函数的逻辑
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, filename, nil, parser.ParseComments)
	if err != nil {
		l.Errorf("Parse error in %s: %v", filename, err)
		return err
	}

	modified := false
	ast.Inspect(file, func(n ast.Node) bool {
		// 处理所有可能包含类型的节点
		switch x := n.(type) {
		case *ast.Field, *ast.ValueSpec, *ast.TypeAssertExpr, *ast.CompositeLit, *ast.CallExpr:
			l.processNode(fset, n, &modified)
		case *ast.FuncType:
			l.processFuncType(fset, x, &modified)
		}
		return true
	})

	if !modified {
		return nil
	}

	if err := l.fixFindMethodsWithSliceAlias(file); err != nil {
		l.Errorf("fix Find methods with slice alias error: %v", err)
		return err
	}

	l.Infof("Processing: %s", filename)
	var buf bytes.Buffer
	if err := format.Node(&buf, fset, file); err != nil {
		l.Errorf("Format error: %v", err)
		return err
	}

	if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
		l.Errorf("Write error: %v", err)
		return err
	}

	return nil
}

// replaceSliceTypeInLine: 在单行中替换切片类型
func (l *GenerateGormDalAndPushToGitlabLogic) replaceSliceTypeInLine(line string) string {
	// 简单的字符串替换实现
	// 查找 []*model.XXX 模式并替换为 model.XXXSlice

	// 使用字符串操作来查找和替换
	result := line

	// 查找所有 []*model. 的位置
	for {
		start := strings.Index(result, "[]*model.")
		if start == -1 {
			break
		}

		// 找到模型名称的结束位置
		modelStart := start + len("[]*model.")
		modelEnd := modelStart

		// 查找模型名称的结束（遇到非字母数字字符）
		for modelEnd < len(result) && (unicode.IsLetter(rune(result[modelEnd])) || unicode.IsDigit(rune(result[modelEnd]))) {
			modelEnd++
		}

		if modelEnd > modelStart {
			modelName := result[modelStart:modelEnd]
			// 替换为 model.XXXSlice
			replacement := "model." + modelName + "Slice"
			result = result[:start] + replacement + result[modelEnd:]
		} else {
			break
		}
	}

	return result
}

// processNode: 处理通用节点 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) processNode(fset *token.FileSet, node ast.Node, modified *bool) {
	var typeExpr ast.Expr
	switch n := node.(type) {
	case *ast.Field:
		typeExpr = n.Type
	case *ast.ValueSpec:
		typeExpr = n.Type
	case *ast.TypeAssertExpr:
		typeExpr = n.Type
	case *ast.CompositeLit:
		typeExpr = n.Type
	case *ast.CallExpr:
		typeExpr = n.Fun
	default:
		return
	}

	if newType := l.replaceType(fset, typeExpr); newType != nil {
		switch n := node.(type) {
		case *ast.Field:
			n.Type = newType
		case *ast.ValueSpec:
			n.Type = newType
		case *ast.TypeAssertExpr:
			n.Type = newType
		case *ast.CompositeLit:
			n.Type = newType
		case *ast.CallExpr:
			n.Fun = newType
		}
		*modified = true
	}
}

// processFuncType: 处理函数类型 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) processFuncType(fset *token.FileSet, funcType *ast.FuncType, modified *bool) {
	// 处理参数
	if funcType.Params != nil {
		for _, field := range funcType.Params.List {
			if newType := l.replaceType(fset, field.Type); newType != nil {
				field.Type = newType
				*modified = true
			}
		}
	}
	// 处理返回值
	if funcType.Results != nil {
		for _, field := range funcType.Results.List {
			if newType := l.replaceType(fset, field.Type); newType != nil {
				field.Type = newType
				*modified = true
			}
		}
	}
}

// collectFilesForCommit: 收集需要提交的文件
func (l *GenerateGormDalAndPushToGitlabLogic) collectFilesForCommit() ([]GitLabCommitAction, error) {
	var actions []GitLabCommitAction

	// 遍历 RepoDir 下的所有文件
	err := filepath.Walk(RepoDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和 .git 文件夹
		if info.IsDir() || strings.Contains(path, ".git") {
			return nil
		}

		// 计算相对路径
		relPath, err := filepath.Rel(RepoDir, path)
		if err != nil {
			return err
		}

		// 读取文件内容
		content, err := os.ReadFile(path)
		if err != nil {
			return err
		}

		// 添加到 actions (action 将在 pushToGitLabAPI 中根据文件是否存在来设置)
		actions = append(actions, GitLabCommitAction{
			Action:   "",                                     // 稍后设置
			FilePath: strings.ReplaceAll(relPath, "\\", "/"), // 确保使用 Unix 路径分隔符
			Content:  string(content),
		})

		return nil
	})

	return actions, err
}

// replaceType: 核心类型替换逻辑 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) replaceType(fset *token.FileSet, typeExpr ast.Expr) ast.Expr {
	switch t := typeExpr.(type) {
	case *ast.StarExpr: // 处理 *[]*model.XXX 情况
		if arrayType, ok := t.X.(*ast.ArrayType); ok {
			if newType := l.checkAndReplaceModelArray(fset, arrayType); newType != nil {
				return &ast.StarExpr{
					Star: t.Star,
					X:    newType,
				}
			}
		}
	case *ast.ArrayType: // 处理 []*model.XXX 情况
		return l.checkAndReplaceModelArray(fset, t)
	}
	return nil
}

// checkAndReplaceModelArray: 检查并替换 model 相关的数组类型 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) checkAndReplaceModelArray(fset *token.FileSet, arrayType *ast.ArrayType) ast.Expr {
	// 处理 *model.XXX
	starExpr, ok := arrayType.Elt.(*ast.StarExpr)
	if !ok {
		return nil
	}

	// 处理 model.XXX
	selExpr, ok := starExpr.X.(*ast.SelectorExpr)
	if !ok {
		return nil
	}

	// 确认是 model 包
	pkgIdent, ok := selExpr.X.(*ast.Ident)
	if !ok || pkgIdent.Name != "model" {
		return nil
	}

	// 构造新的类型 model.XXXSlice
	return &ast.SelectorExpr{
		X:   &ast.Ident{Name: "model", NamePos: pkgIdent.NamePos},
		Sel: &ast.Ident{Name: selExpr.Sel.Name + "Slice", NamePos: selExpr.Sel.NamePos},
	}
}

// fixFindMethodsWithSliceAlias: 修复 Find 方法的实现 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) fixFindMethodsWithSliceAlias(file *ast.File) error {
	needsFmtImport := false
	ast.Inspect(file, func(n ast.Node) bool {
		fn, ok := n.(*ast.FuncDecl)
		if !ok {
			return true
		}

		// 只处理Find方法
		if fn.Name.Name != "Find" {
			return true
		}

		// 检查返回类型是否是(model.XSlice, error)
		if fn.Type.Results == nil || len(fn.Type.Results.List) != 2 {
			return true
		}

		returnType, ok := fn.Type.Results.List[0].Type.(*ast.SelectorExpr)
		if !ok || !strings.HasSuffix(returnType.Sel.Name, "Slice") {
			return true
		}

		// 提取模型信息
		modelPkg := returnType.X.(*ast.Ident).Name
		baseType := strings.TrimSuffix(returnType.Sel.Name, "Slice")

		// 获取接收者名称
		receiverName := "c"
		if fn.Recv != nil && len(fn.Recv.List) > 0 && len(fn.Recv.List[0].Names) > 0 {
			receiverName = fn.Recv.List[0].Names[0].Name
		}

		// 构建新的函数体
		fn.Body.List = l.buildFindMethodBody(modelPkg, baseType, receiverName)
		needsFmtImport = true
		return true
	})

	if needsFmtImport {
		l.addFmtImport(file)
	}
	return nil
}

// buildFindMethodBody: 构建 Find 方法的新函数体 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) buildFindMethodBody(modelPkg, baseType, receiverName string) []ast.Stmt {
	// 完全移植原始代码的逻辑
	return []ast.Stmt{
		// result, err := c.DO.Find()
		&ast.AssignStmt{
			Lhs: []ast.Expr{ast.NewIdent("result"), ast.NewIdent("err")},
			Tok: token.DEFINE,
			Rhs: []ast.Expr{
				&ast.CallExpr{
					Fun: &ast.SelectorExpr{
						X:   &ast.SelectorExpr{X: ast.NewIdent(receiverName), Sel: ast.NewIdent("DO")},
						Sel: ast.NewIdent("Find"),
					},
				},
			},
		},
		// if err != nil { return nil, err }
		&ast.IfStmt{
			Cond: &ast.BinaryExpr{X: ast.NewIdent("err"), Op: token.NEQ, Y: ast.NewIdent("nil")},
			Body: &ast.BlockStmt{
				List: []ast.Stmt{
					&ast.ReturnStmt{
						Results: []ast.Expr{
							&ast.CompositeLit{
								Type: &ast.SelectorExpr{
									X:   ast.NewIdent(modelPkg),
									Sel: ast.NewIdent(baseType + "Slice"),
								},
							},
							ast.NewIdent("err"),
						},
					},
				},
			},
		},
		// if slice, ok := result.([]*model.X); ok {
		&ast.IfStmt{
			Init: &ast.AssignStmt{
				Lhs: []ast.Expr{ast.NewIdent("slice"), ast.NewIdent("ok")},
				Tok: token.DEFINE,
				Rhs: []ast.Expr{
					&ast.TypeAssertExpr{
						X: ast.NewIdent("result"),
						Type: &ast.ArrayType{
							Elt: &ast.StarExpr{
								X: &ast.SelectorExpr{
									X:   ast.NewIdent(modelPkg),
									Sel: ast.NewIdent(baseType),
								},
							},
						},
					},
				},
			},
			Cond: ast.NewIdent("ok"),
			Body: &ast.BlockStmt{
				List: []ast.Stmt{
					// return model.XSlice(slice), err
					&ast.ReturnStmt{
						Results: []ast.Expr{
							&ast.CallExpr{
								Fun: &ast.SelectorExpr{
									X:   ast.NewIdent(modelPkg),
									Sel: ast.NewIdent(baseType + "Slice"),
								},
								Args: []ast.Expr{ast.NewIdent("slice")},
							},
							ast.NewIdent("err"),
						},
					},
				},
			},
		},
		// return nil, err
		&ast.ReturnStmt{
			Results: []ast.Expr{
				&ast.CompositeLit{
					Type: &ast.SelectorExpr{
						X:   ast.NewIdent(modelPkg),
						Sel: ast.NewIdent(baseType + "Slice"),
					},
				},
				ast.NewIdent("err"),
			},
		},
	}
}

// addFmtImport: 添加 fmt 导入 (直接移植自 convert_query.go)
func (l *GenerateGormDalAndPushToGitlabLogic) addFmtImport(file *ast.File) {
	for _, imp := range file.Imports {
		if imp.Path.Value == `"fmt"` {
			return
		}
	}

	file.Imports = append(file.Imports, &ast.ImportSpec{
		Path: &ast.BasicLit{
			Kind:  token.STRING,
			Value: `"fmt"`,
		},
	})

	// 确保有import声明
	hasImportDecl := false
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.IMPORT {
			hasImportDecl = true
			break
		}
	}

	if !hasImportDecl {
		importDecl := &ast.GenDecl{
			Tok:   token.IMPORT,
			Specs: []ast.Spec{file.Imports[len(file.Imports)-1]},
		}
		file.Decls = append([]ast.Decl{importDecl}, file.Decls...)
	}
}

// sendCommitToGitLab: 发送提交到 GitLab API
func (l *GenerateGormDalAndPushToGitlabLogic) sendCommitToGitLab(commitReq GitLabCommitRequest, token string) error {
	// 序列化请求
	jsonData, err := json.Marshal(commitReq)
	if err != nil {
		return fmt.Errorf("failed to marshal commit request: %v", err)
	}

	// 构造 API URL
	url := fmt.Sprintf("%s/projects/%s/repository/commits", GitLabAPI, strings.ReplaceAll(ProjectID, "/", "%2F"))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("GitLab API error (status %d): %s", resp.StatusCode, string(body))
	}

	l.Infof("✅ Successfully committed to GitLab: %s", resp.Status)
	return nil
}

// getExistingFiles: 获取 GitLab 仓库中现有的文件列表
func (l *GenerateGormDalAndPushToGitlabLogic) getExistingFiles(token string) (map[string]bool, error) {
	// 构造 API URL - 获取仓库树
	url := fmt.Sprintf("%s/projects/%s/repository/tree?recursive=true&per_page=1000",
		GitLabAPI, strings.ReplaceAll(ProjectID, "/", "%2F"))

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+token)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GitLab API error (status %d): %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var treeItems []struct {
		Path string `json:"path"`
		Type string `json:"type"`
	}

	if err := json.Unmarshal(body, &treeItems); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	// 构建文件映射
	existingFiles := make(map[string]bool)
	for _, item := range treeItems {
		if item.Type == "blob" { // 只关心文件，不关心目录
			existingFiles[item.Path] = true
			l.Infof("🔍 Found existing file: %s", item.Path)
		}
	}

	l.Infof("📋 Found %d existing files in repository", len(existingFiles))
	return existingFiles, nil
}

// deleteAndRecreateStrategy: 使用 Git 命令删除目录，然后用 GitLab API 创建文件
func (l *GenerateGormDalAndPushToGitlabLogic) deleteAndRecreateStrategy(actions []GitLabCommitAction, token string) error {
	// 步骤1: 识别需要删除的目录
	siteDirs := l.identifySiteDirectories(actions)

	if len(siteDirs) > 0 {
		l.Infof("🗑️ Deleting %d site directories using Git: %v", len(siteDirs), siteDirs)

		// 使用 Git 命令删除目录
		if err := l.deleteDirectoriesWithGit(siteDirs, token); err != nil {
			l.Errorf("Failed to delete directories with Git: %v", err)
			return fmt.Errorf("git delete failed: %v", err)
		}

		l.Info("✅ Directories deleted successfully with Git")
	}

	// 步骤2: 使用 CREATE action 创建所有文件
	l.Info("📁 Creating all files with CREATE action")
	for i := range actions {
		actions[i].Action = "create"
	}

	// 创建重建请求
	createReq := GitLabCommitRequest{
		Branch:        "main",
		CommitMessage: fmt.Sprintf("auto: regenerate dal code [%s]", time.Now().Format("2006-01-02 15:04:05")),
		Actions:       actions,
	}

	// 执行创建操作
	if err := l.sendCommitToGitLab(createReq, token); err != nil {
		return fmt.Errorf("failed to create files: %v", err)
	}

	l.Info("✅ All files created successfully")
	return nil
}

// identifySiteDirectories: 识别需要删除的 site 目录
func (l *GenerateGormDalAndPushToGitlabLogic) identifySiteDirectories(actions []GitLabCommitAction) []string {
	dirSet := make(map[string]bool)

	for _, action := range actions {
		// 提取 site 目录路径，例如 "ops/dal/model/user.gen.go" -> "ops"
		parts := strings.Split(action.FilePath, "/")
		if len(parts) > 0 {
			siteDir := parts[0] // 第一级目录就是 site 名称
			dirSet[siteDir] = true
		}
	}

	// 转换为切片
	dirs := make([]string, 0, len(dirSet))
	for dir := range dirSet {
		dirs = append(dirs, dir)
	}

	return dirs
}

// checkExistingDirectories: 检查哪些目录实际存在
func (l *GenerateGormDalAndPushToGitlabLogic) checkExistingDirectories(dirs []string, token string) []string {
	var existingDirs []string

	for _, dir := range dirs {
		// 检查目录下是否有任何文件（通过查询目录树）
		url := fmt.Sprintf("%s/projects/%s/repository/tree?path=%s&ref=main",
			GitLabAPI, strings.ReplaceAll(ProjectID, "/", "%2F"), strings.ReplaceAll(dir, "/", "%2F"))

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			l.Errorf("Failed to create request for directory %s: %v", dir, err)
			continue
		}

		req.Header.Set("Authorization", "Bearer "+token)

		client := &http.Client{Timeout: 10 * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			l.Errorf("Failed to check directory %s: %v", dir, err)
			continue
		}
		resp.Body.Close()

		// 200 = 目录存在且有内容, 404 = 目录不存在或为空
		if resp.StatusCode == http.StatusOK {
			existingDirs = append(existingDirs, dir)
			l.Infof("✅ Directory exists: %s", dir)
		} else {
			l.Infof("📂 Directory not found or empty: %s", dir)
		}
	}

	return existingDirs
}

// filterFilesToDelete: 过滤出需要删除的文件（只删除我们要重新生成的文件）
func (l *GenerateGormDalAndPushToGitlabLogic) filterFilesToDelete(existingFiles map[string]bool, actions []GitLabCommitAction) []string {
	var filesToDelete []string

	// 创建要生成的文件路径映射
	generateFiles := make(map[string]bool)
	for _, action := range actions {
		generateFiles[action.FilePath] = true
	}

	// 只删除我们要重新生成的现有文件
	for filePath := range existingFiles {
		if generateFiles[filePath] {
			filesToDelete = append(filesToDelete, filePath)
		}
	}

	return filesToDelete
}

// deleteDirectoriesWithGit: 使用 Git 命令删除目录
func (l *GenerateGormDalAndPushToGitlabLogic) deleteDirectoriesWithGit(siteDirs []string, token string) error {
	// 创建临时目录
	tempDir := fmt.Sprintf("/tmp/dal-cleanup-%d", time.Now().Unix())
	defer os.RemoveAll(tempDir)

	// 构造仓库 URL
	repoURL := fmt.Sprintf("https://oauth2:%<EMAIL>/%s.git", token, ProjectID)

	l.Infof("📥 Cloning repository to %s", tempDir)

	// 克隆仓库
	cloneCmd := exec.Command("git", "clone", "--depth=1", repoURL, tempDir)
	if output, err := cloneCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to clone repository: %v, output: %s", err, output)
	}

	// 切换到仓库目录
	oldDir, _ := os.Getwd()
	defer os.Chdir(oldDir)
	os.Chdir(tempDir)

	// 删除目录
	hasChanges := false
	for _, dir := range siteDirs {
		dirPath := filepath.Join(tempDir, dir)
		if _, err := os.Stat(dirPath); err == nil {
			l.Infof("🗑️ Removing directory: %s", dir)
			if err := os.RemoveAll(dirPath); err != nil {
				l.Errorf("Failed to remove directory %s: %v", dir, err)
				continue
			}
			hasChanges = true
		} else {
			l.Infof("📂 Directory not found, skipping: %s", dir)
		}
	}

	if !hasChanges {
		l.Info("📁 No directories to delete")
		return nil
	}

	// 添加变更
	addCmd := exec.Command("git", "add", "-A")
	if output, err := addCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to add changes: %v, output: %s", err, output)
	}

	// 提交变更
	commitMsg := fmt.Sprintf("auto: cleanup dal directories [%s]", time.Now().Format("2006-01-02 15:04:05"))
	commitCmd := exec.Command("git", "commit", "-m", commitMsg)
	if output, err := commitCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to commit changes: %v, output: %s", err, output)
	}

	// 推送变更
	pushCmd := exec.Command("git", "push", "origin", "main")
	if output, err := pushCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to push changes: %v, output: %s", err, output)
	}

	l.Info("✅ Successfully deleted directories and pushed changes")
	return nil
}

// configureGitUser: 配置 Git 用户信息
func (l *GenerateGormDalAndPushToGitlabLogic) configureGitUser() error {
	// 设置 Git 用户名和邮箱
	if err := l.runGitCommand("config", "user.name", "GORM DAL Generator"); err != nil {
		return fmt.Errorf("failed to set git user.name: %v", err)
	}

	if err := l.runGitCommand("config", "user.email", "<EMAIL>"); err != nil {
		return fmt.Errorf("failed to set git user.email: %v", err)
	}

	return nil
}

// runGitCommand: 执行 Git 命令
func (l *GenerateGormDalAndPushToGitlabLogic) runGitCommand(args ...string) error {
	cmd := exec.Command("git", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("git command failed: %v", err)
	}

	return nil
}

// checkForChanges: 检查是否有待提交的变更
func (l *GenerateGormDalAndPushToGitlabLogic) checkForChanges() (bool, error) {
	cmd := exec.Command("git", "status", "--porcelain")
	output, err := cmd.Output()
	if err != nil {
		return false, fmt.Errorf("failed to check git status: %v", err)
	}

	// 如果输出为空，说明没有变更
	return len(strings.TrimSpace(string(output))) > 0, nil
}
