package types

import (
	"encoding/json"
)

type I18nPackageMeta struct {
	Version            string   `json:"version"`            // 语言包版本
	GeneratedAt        string   `json:"generatedAt"`        // 生成时间
	Namespace          string   `json:"namespace"`          // 命名空间
	Languages          []string `json:"languages"`          // 包含的语言列表
	IOSAppVersions     []string `json:"iosAppVersions"`     // 适用的 iOS App 版本列表
	AndroidAppVersions []string `json:"androidAppVersions"` // 适用的 Android App 版本列表
}

func (f FeatureConfig) MarshalJSON() ([]byte, error) {
	// 构建最终输出的 map
	out := make(map[string]interface{})
	out["enabled"] = f.Enabled
	// 把 Config 中的所有 key-value 提升到外层
	for k, v := range f.Config {
		out[k] = v
	}
	return json.Marshal(out)
}

func (f *FeatureConfig) UnmarshalJSON(data []byte) error {
	// 先解析成通用 map
	var raw map[string]interface{}
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	// 初始化 Config map
	f.Config = make(map[string]interface{})

	// 处理每个字段
	for k, v := range raw {
		if k == "enabled" {
			if b, ok := v.(bool); ok {
				f.Enabled = b
			}
		} else {
			// 其他字段都放入 Config
			f.Config[k] = v
		}
	}

	return nil
}
