package release

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/release"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func CreateFeishuApprovalForReleasePlanHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PlanIdReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := release.NewCreateFeishuApprovalForReleasePlanLogic(r.Context(), svcCtx)
		resp, err := l.CreateFeishuApprovalForReleasePlan(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
