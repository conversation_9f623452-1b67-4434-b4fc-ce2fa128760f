package ops

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/ops"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func GenerateGormDalAndPushToGitlabHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GenerateGormDalAndPushToGitlabReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := ops.NewGenerateGormDalAndPushToGitlabLogic(r.Context(), svcCtx)
		resp, err := l.GenerateGormDalAndPushToGitlab(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
