package i18n

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/i18n"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func TriggerI18nExportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.TriggerI18nExportReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := i18n.NewTriggerI18nExportLogic(r.Context(), svcCtx)
		resp, err := l.TriggerI18nExport(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
