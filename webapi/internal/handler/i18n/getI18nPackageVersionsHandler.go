package i18n

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/i18n"
	"webapi/internal/svc"
)

func GetI18nPackageVersionsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := i18n.NewGetI18nPackageVersionsLogic(r.Context(), svcCtx)
		resp, err := l.GetI18nPackageVersions()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
