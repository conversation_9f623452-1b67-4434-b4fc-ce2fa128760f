package fulfillment_center

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/fulfillment_center"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func UpdateProductMediaTypeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateProductMediaTypeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := fulfillment_center.NewUpdateProductMediaTypeLogic(r.Context(), svcCtx)
		resp, err := l.UpdateProductMediaType(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
