# 飞书审批功能使用指南

## 功能概述

在发布计划管理系统中，我们新增了飞书审批功能，允许用户在系统内审批完成后，进一步提交到飞书进行外部审批流程。

## 功能位置

### 1. 发布计划列表页面 (`/release/plans`)

在发布计划列表的操作列中，当满足以下条件时会显示"飞书审批"按钮：
- 发布计划已有系统内审批实例 (`approval_instance_id` 不为空)
- 系统内审批状态为"已完成" (`status === 'completed'`)
- 尚未提交过飞书审批 (`feishu_approval_instance_id` 为空)

### 2. 发布计划详情页面 (`/release/plans/:id`)

在发布计划详情页面的顶部操作区域，当满足相同条件时会显示"提交飞书审批"按钮。

## 使用流程

### 步骤1：完成系统内审批
1. 创建发布计划
2. 提交系统内审批
3. 等待审批流程完成，状态变为"审批已通过"

### 步骤2：提交飞书审批
1. 在发布计划列表或详情页面，点击"飞书审批"或"提交飞书审批"按钮
2. 系统会自动创建飞书审批实例
3. 成功后会显示"飞书审批提交成功"的消息

### 步骤3：在飞书中处理审批
1. 相关审批人会在飞书中收到审批通知
2. 审批人可以在飞书应用中查看和处理审批请求

## 审批表单内容

飞书审批表单会自动填充以下信息：

### 基本信息
- **项目名称**: "电商系统-" + 发布计划名称
- **期望更新时间**: 当天 18:30
- **申请人**: 当前用户邮箱
- **验收人**: <EMAIL>
- **知会项目组其他成员**: <EMAIL>

### 发布详情
- **发布明细**: 包含所有应用任务的详细信息
  - 应用服务名称
  - 环境属性：生产PROD
  - 分支名称
  - 是否涉及SQL：否

### 发布内容
- **发布内容**: 发布计划描述 + 所有相关链接
  - 合并请求链接
  - PRD文档链接
  - 技术设计文档链接
  - 发布手册链接

### 固定设置
- **测试结果**: 通过
- **环境属性**: 生产PROD
- **是否涉及SQL**: 否

## 环境配置

系统会根据配置文件中的 `FeiShu.AppId` 自动识别环境：

### 测试环境
- AppId: `cli_a8fa0c729a655013`
- 使用测试环境的飞书审批模板

### 生产环境  
- AppId: `cli_a7d56bbafb7e100b`
- 使用生产环境的飞书审批模板

## 注意事项

1. **前置条件**: 必须先完成系统内审批流程，才能提交飞书审批
2. **重复提交**: 每个发布计划只能提交一次飞书审批
3. **权限要求**: 用户必须有有效的邮箱，且能在飞书中找到对应用户
4. **网络要求**: 需要能够访问飞书API服务

## 错误处理

### 常见错误及解决方案

1. **"请先创建系统内审批流程"**
   - 原因：发布计划尚未创建系统内审批
   - 解决：先点击"提交审批"按钮创建系统内审批

2. **"系统内审批流程尚未完成"**
   - 原因：系统内审批还在进行中或被拒绝
   - 解决：等待审批完成或重新提交审批

3. **"该发布计划已创建飞书审批"**
   - 原因：已经提交过飞书审批
   - 解决：无需重复提交，可在飞书中查看审批状态

4. **"飞书审批提交失败"**
   - 原因：网络问题或飞书API异常
   - 解决：检查网络连接，稍后重试

## 技术实现

### 前端组件
- **发布计划列表**: `web/src/pages/Release/ReleasePlans.tsx`
- **发布计划详情**: `web/src/pages/Release/ReleasePlanDetail.tsx`
- **API服务**: `web/src/services/ant-design-pro/api.ts`
- **类型定义**: `web/src/services/ant-design-pro/typings.d.ts`

### 后端接口
- **API端点**: `POST /api/release/plans/:plan_id/feishu-approvals`
- **处理器**: `webapi/internal/handler/release/createFeishuApprovalForReleasePlanHandler.go`
- **业务逻辑**: `webapi/internal/logic/release/createFeishuApprovalForReleasePlanLogic.go`
- **飞书服务**: `webapi/internal/third/lark/lark_service.go`
- **模板配置**: `webapi/internal/third/lark/approval_templates.go`

### 数据库字段
- **表名**: `release_plans`
- **字段**: `feishu_approval_instance_id VARCHAR(255)`
- **用途**: 存储飞书审批实例ID，用于防重复提交和状态跟踪

## 更新日志

### v1.0.0 (2025-08-04)
- ✅ 新增飞书审批功能
- ✅ 支持测试和生产环境自动识别
- ✅ 模板化表单构建
- ✅ 前端页面集成
- ✅ 完整的错误处理机制
