.dal-generator {
  .site-card {
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    
    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
    
    .ant-checkbox-wrapper {
      width: 100%;
      
      .ant-checkbox {
        margin-right: 8px;
      }
    }
  }
  
  .site-card.selected {
    border-color: #1890ff;
    background-color: #f6ffed;
  }
  
  .generate-button {
    min-width: 120px;
    height: 40px;
    font-size: 16px;
    border-radius: 6px;
  }
  
  .result-card {
    .ant-alert {
      border-radius: 6px;
    }
    
    .timeline-container {
      max-width: 400px;
      margin: 24px auto 0;
      
      .ant-timeline-item-content {
        font-size: 14px;
      }
    }
  }
  
  .history-modal {
    .ant-list-item {
      padding: 12px 0;
      
      .ant-list-item-meta-avatar {
        margin-right: 12px;
      }
      
      .ant-list-item-meta-title {
        margin-bottom: 4px;
        font-size: 14px;
      }
      
      .ant-list-item-meta-description {
        font-size: 13px;
        color: #666;
      }
    }
  }
  
  .info-card {
    .ant-alert-description {
      ul {
        margin: 8px 0;
        padding-left: 20px;
        
        li {
          margin: 4px 0;
          font-size: 14px;
        }
      }
    }
  }
  
  .loading-container {
    text-align: center;
    padding: 40px;
    
    .ant-spin {
      margin-bottom: 16px;
    }
    
    .loading-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 24px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dal-generator {
    .site-card {
      margin-bottom: 12px;
    }
    
    .generate-button {
      width: 100%;
      margin-bottom: 12px;
    }
    
    .timeline-container {
      margin: 16px auto 0;
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .dal-generator {
    .site-card {
      border-color: #434343;
      background-color: #1f1f1f;
      
      &:hover {
        border-color: #1890ff;
        background-color: #262626;
      }
    }
    
    .site-card.selected {
      background-color: #162312;
      border-color: #52c41a;
    }
  }
}
