import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Button,
  Space,
  message,
  Typography,
  Form,
  Checkbox,
  Alert,
  Spin,
  Divider,
  Tag,
  Row,
  Col,
  Timeline,
  Modal,
  List,
} from 'antd';
import {
  CodeOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  GitlabOutlined,
} from '@ant-design/icons';
import { generateGormDalAndPushToGitlab } from '@/services/ant-design-pro/api';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

interface GenerateResult {
  success: boolean;
  message: string;
  timestamp: string;
}

const DalGenerator: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<GenerateResult | null>(null);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [history, setHistory] = useState<GenerateResult[]>([]);

  // 支持的站点配置
  const siteOptions = [
    {
      value: 'ops',
      label: 'OPS 运维平台',
      description: '运维平台相关数据库表',
      database: 'bo_service_data_ops',
      color: 'blue',
    },
    {
      value: 'fulfillment',
      label: 'Fulfillment 履约中心',
      description: '履约中心相关数据库表（支持多地区）',
      database: 'bo_service_ec',
      color: 'green',
    },
    {
      value: 'ohsome',
      label: 'Ohsome 独立站',
      description: '独立站相关数据库表（支持多地区）',
      database: 'shop_mall',
      color: 'orange',
    },
    {
      value: 'membership',
      label: 'Membership 会员系统',
      description: '会员系统相关数据库表',
      database: 'member_center',
      color: 'purple',
    },
  ];

  // 处理生成 DAL
  const handleGenerate = async () => {
    try {
      const values = await form.validateFields();
      if (!values.sites || values.sites.length === 0) {
        message.warning('请至少选择一个站点');
        return;
      }

      setLoading(true);
      setResult(null);

      const response = await generateGormDalAndPushToGitlab({
        databaseNames: values.sites,
      });

      const newResult: GenerateResult = {
        success: response.success || false,
        message: response.errorMessage || response.message || '生成完成',
        timestamp: new Date().toLocaleString(),
      };

      setResult(newResult);
      
      // 添加到历史记录
      setHistory(prev => [newResult, ...prev.slice(0, 9)]); // 保留最近10条记录

      if (response.success) {
        message.success('DAL 代码生成并推送成功！');
      } else {
        message.error(`生成失败: ${response.errorMessage || '未知错误'}`);
      }
    } catch (error) {
      console.error('生成 DAL 失败:', error);
      const errorResult: GenerateResult = {
        success: false,
        message: `请求失败: ${error}`,
        timestamp: new Date().toLocaleString(),
      };
      setResult(errorResult);
      setHistory(prev => [errorResult, ...prev.slice(0, 9)]);
      message.error('生成 DAL 失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setResult(null);
  };

  return (
    <PageContainer
      title="GORM DAL 代码生成器"
      subTitle="为指定站点生成 GORM DAL 代码并推送到 GitLab"
      extra={[
        <Button
          key="history"
          icon={<ReloadOutlined />}
          onClick={() => setHistoryVisible(true)}
        >
          查看历史
        </Button>,
        <Button
          key="reset"
          onClick={handleReset}
          disabled={loading}
        >
          重置
        </Button>,
      ]}
    >
      <div className={styles.dalGenerator} style={{ background: '#f5f5f5', minHeight: '100vh', padding: '0' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 功能说明 */}
          <Card
            title={
              <Space>
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
                <span>功能说明</span>
              </Space>
            }
            style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}
          >
            <Alert
              message="GORM DAL 代码生成器"
              description={
                <div>
                  <Paragraph>
                    此工具会为选定的站点自动生成 GORM DAL（数据访问层）代码，包括：
                  </Paragraph>
                  <ul style={{ marginLeft: '20px', marginBottom: '16px' }}>
                    <li>数据库表对应的 GORM 模型文件</li>
                    <li>查询接口和 CRUD 操作方法</li>
                    <li>模型切片别名（如 UserSlice []*User）</li>
                    <li>支持多地区的 DAL 初始化文件</li>
                  </ul>
                  <Paragraph>
                    生成的代码将自动推送到 GitLab 仓库：
                    <Text code>https://git.blueorigin.work/e-commerce/gorm-generated-dal</Text>
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
              style={{ background: '#e6f7ff', border: '1px solid #91d5ff' }}
            />
          </Card>

          {/* 站点选择 */}
          <Card
            title={
              <Space>
                <CodeOutlined style={{ color: '#52c41a' }} />
                <span>选择站点</span>
              </Space>
            }
            style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}
          >
            <Form form={form} layout="vertical">
              <Form.Item
                name="sites"
                label="请选择需要生成 DAL 代码的站点"
                rules={[{ required: true, message: '请至少选择一个站点' }]}
              >
                <Checkbox.Group style={{ width: '100%' }}>
                  <Row gutter={[16, 16]}>
                    {siteOptions.map((site) => (
                      <Col span={12} key={site.value}>
                        <Card
                          size="small"
                          className={styles.siteCard}
                          bodyStyle={{ padding: '12px' }}
                          hoverable
                        >
                          <Checkbox value={site.value} style={{ width: '100%' }}>
                            <div style={{ marginLeft: '8px' }}>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                                <Text strong>{site.label}</Text>
                                <Tag color={site.color} size="small" style={{ marginLeft: '8px' }}>
                                  {site.value}
                                </Tag>
                              </div>
                              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                                {site.description}
                              </Text>
                              <Text code style={{ fontSize: '11px', color: '#666' }}>
                                {site.database}
                              </Text>
                            </div>
                          </Checkbox>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Form>

            <Divider />

            <div style={{ textAlign: 'center' }}>
              <Space size="middle">
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handleGenerate}
                  loading={loading}
                  className={styles.generateButton}
                >
                  {loading ? '生成中...' : '开始生成'}
                </Button>
                <Button size="large" onClick={handleReset} disabled={loading}>
                  重置选择
                </Button>
              </Space>
            </div>
          </Card>

          {/* 生成结果 */}
          {(result || loading) && (
            <Card
              title={
                <Space>
                  {loading ? (
                    <Spin size="small" />
                  ) : result?.success ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                  )}
                  <span>生成结果</span>
                </Space>
              }
              style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}
            >
              {loading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <Spin size="large" />
                  <div style={{ marginTop: '16px' }}>
                    <Text>正在生成 DAL 代码并推送到 GitLab...</Text>
                  </div>
                  <Timeline
                    style={{ marginTop: '24px', textAlign: 'left', maxWidth: '400px', margin: '24px auto 0' }}
                    items={[
                      { children: '连接到站点数据库', color: 'green' },
                      { children: '扫描数据库表结构', color: 'blue' },
                      { children: '生成 GORM 模型文件' },
                      { children: '生成查询接口' },
                      { children: '推送到 GitLab 仓库' },
                    ]}
                  />
                </div>
              ) : (
                <Alert
                  message={result?.success ? '生成成功' : '生成失败'}
                  description={
                    <div>
                      <Paragraph>{result?.message}</Paragraph>
                      <Text type="secondary">时间: {result?.timestamp}</Text>
                      {result?.success && (
                        <div style={{ marginTop: '12px' }}>
                          <Button
                            type="link"
                            icon={<GitlabOutlined />}
                            href="https://git.blueorigin.work/e-commerce/gorm-generated-dal"
                            target="_blank"
                            style={{ paddingLeft: 0 }}
                          >
                            查看 GitLab 仓库
                          </Button>
                        </div>
                      )}
                    </div>
                  }
                  type={result?.success ? 'success' : 'error'}
                  showIcon
                />
              )}
            </Card>
          )}
        </Space>
      </div>

      {/* 历史记录弹窗 */}
      <Modal
        title="生成历史记录"
        open={historyVisible}
        onCancel={() => setHistoryVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHistoryVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
        className={styles.historyModal}
      >
        {history.length > 0 ? (
          <List
            dataSource={history}
            renderItem={(item, index) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    item.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
                    ) : (
                      <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
                    )
                  }
                  title={
                    <Space>
                      <Text strong>{item.success ? '生成成功' : '生成失败'}</Text>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {item.timestamp}
                      </Text>
                    </Space>
                  }
                  description={item.message}
                />
              </List.Item>
            )}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">暂无历史记录</Text>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default DalGenerator;
