# GORM DAL 代码生成器

## 功能概述

GORM DAL 代码生成器是一个用于自动生成 GORM 数据访问层代码的工具，支持多站点数据库的代码生成并自动推送到 GitLab 仓库。

## 主要功能

### 1. 站点选择
- **OPS 运维平台**: 生成运维平台相关数据库表的 DAL 代码
- **Fulfillment 履约中心**: 生成履约中心相关数据库表的 DAL 代码（支持多地区）
- **Ohsome 独立站**: 生成独立站相关数据库表的 DAL 代码（支持多地区）
- **Membership 会员系统**: 生成会员系统相关数据库表的 DAL 代码

### 2. 代码生成
- 自动扫描指定站点数据库中的所有表
- 生成对应的 GORM 模型文件
- 生成查询接口和 CRUD 操作方法
- 生成模型切片别名（如 `UserSlice []*User`）
- 生成支持多地区的 DAL 初始化文件

### 3. GitLab 集成
- 自动推送生成的代码到 GitLab 仓库
- 仓库地址: `https://git.blueorigin.work/e-commerce/gorm-generated-dal`
- 目录结构: `${site_name}/dal/query` 和 `${site_name}/dal/model`

## 使用方法

1. **选择站点**: 在页面中勾选需要生成 DAL 代码的站点
2. **开始生成**: 点击"开始生成"按钮
3. **等待完成**: 系统会自动完成以下步骤：
   - 连接到站点数据库
   - 扫描数据库表结构
   - 生成 GORM 模型文件
   - 生成查询接口
   - 推送到 GitLab 仓库
4. **查看结果**: 生成完成后可以查看结果信息和访问 GitLab 仓库

## 技术实现

### 后端 API
- **接口**: `POST /api/ops/gorm/dal/generate`
- **请求参数**: `{ "databaseNames": ["ops", "fulfillment", "ohsome"] }`
- **响应**: `{ "success": true, "message": "生成成功" }`

### 前端组件
- 使用 React + TypeScript 开发
- 基于 Ant Design 组件库
- 支持响应式设计和深色主题

### 数据库连接
- 使用 `datasource.GetDsSiteDefault(siteName)` 获取站点特定的数据库连接
- 支持多地区数据库配置
- 自动查询当前数据库的所有表

## 文件结构

```
web/src/pages/Ops/DalGenerator/
├── index.tsx          # 主组件文件
├── index.less         # 样式文件
└── README.md          # 说明文档
```

## 样式特性

- 响应式设计，支持移动端访问
- 深色主题适配
- 平滑的动画过渡效果
- 现代化的卡片式布局

## 注意事项

1. **权限要求**: 需要登录并具有相应的操作权限
2. **网络连接**: 需要能够访问数据库和 GitLab 仓库
3. **GitLab Token**: 后端需要配置有效的 GitLab Token
4. **数据库连接**: 确保各站点的数据库连接配置正确

## 错误处理

- 网络请求失败时会显示错误信息
- 支持查看历史生成记录
- 提供详细的错误描述和解决建议

## 扩展功能

- 历史记录查看
- 实时生成进度显示
- 一键访问 GitLab 仓库
- 支持批量选择站点
