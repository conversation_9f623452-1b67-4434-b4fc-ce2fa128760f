import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Space, Typography, Tag, Button, message, Modal, Form, Input, Select } from 'antd';
import { DownloadOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons';
import { getI18nPackageVersions, exportI18nPackage } from '@/services/ant-design-pro/api';
import { LanguageOptions, I18nNamespaceOptions } from '@/constants/enum';

const { Title, Text } = Typography;

const PackageVersions: React.FC = () => {
  const [packageVersions, setPackageVersions] = useState<API.I18nPackageVersion[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm();

  // 获取语言包版本列表
  const fetchPackageVersions = async () => {
    setLoading(true);
    try {
      const response = await getI18nPackageVersions();
      setPackageVersions(response.data || []);
    } catch (error) {
      message.error('获取语言包版本失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackageVersions();
  }, []);

  // 格式化文件大小
  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  };

  // 格式化语言列表
  const formatLanguages = (languages: string[]) => {
    if (!languages || languages.length === 0) return '-';
    return languages.map(lang => (
      <Tag key={lang} color="blue" style={{ margin: '2px' }}>
        {lang}
      </Tag>
    ));
  };

  // 触发导出
  const handleTriggerExport = async (values: any) => {
    setExportLoading(true);
    try {
      const response = await exportI18nPackage({
        tag: values.tag,
        namespace: values.namespace || 'ohsome-app',
        languages: values.languages,
        iosAppVersions: values.iosAppVersions || [],
        androidAppVersions: values.androidAppVersions || [],
      });

      if (response.success) {
        message.success('语言包导出成功！');
        if (response.data) {
          // 显示下载链接
          const links = [];
          if (response.data.androidUrl) {
            links.push(`Android: ${response.data.androidUrl}`);
          }
          if (response.data.iosUrl) {
            links.push(`iOS: ${response.data.iosUrl}`);
          }
          if (links.length > 0) {
            message.info(`下载链接：${links.join(', ')}`);
          }
        }
        setExportModalVisible(false);
        form.resetFields();
        // 刷新列表
        fetchPackageVersions();
      } else {
        message.error(response.message || '导出失败');
      }
    } catch (error) {
      message.error('导出失败');
    } finally {
      setExportLoading(false);
    }
  };



  // 表格列定义
  const columns = [
    {
      title: '设备类型',
      dataIndex: 'device',
      key: 'device',
      width: 100,
      render: (device: string) => (
        <Tag color={device === 'android' ? 'green' : 'blue'}>
          {device.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
      width: 150,
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 120,
      render: (namespace: string) => {
        if (!namespace) {
          return <Text type="secondary">未知</Text>;
        }
        return <Tag color="purple">{namespace}</Tag>;
      },
    },
    {
      title: '包含语言',
      dataIndex: 'languages',
      key: 'languages',
      width: 300,
      render: (languages: string[]) => formatLanguages(languages),
    },
    {
      title: 'iOS App 版本',
      dataIndex: 'iosAppVersions',
      key: 'iosAppVersions',
      width: 200,
      render: (versions: string[]) => {
        if (!versions || versions.length === 0) {
          return <Text type="secondary">未指定</Text>;
        }
        return (
          <div>
            {versions.map((version, index) => (
              <Tag key={index} color="blue" style={{ marginBottom: 2 }}>
                {version}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: 'Android App 版本',
      dataIndex: 'androidAppVersions',
      key: 'androidAppVersions',
      width: 200,
      render: (versions: string[]) => {
        if (!versions || versions.length === 0) {
          return <Text type="secondary">未指定</Text>;
        }
        return (
          <div>
            {versions.map((version, index) => (
              <Tag key={index} color="green" style={{ marginBottom: 2 }}>
                {version}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      title: '生成时间',
      dataIndex: 'generatedAt',
      key: 'generatedAt',
      width: 180,
      render: (time: string) => {
        if (!time) {
          return <Text type="secondary">未知</Text>;
        }
        return new Date(time).toLocaleString();
      },
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      key: 'uploadTime',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: 'CDN 链接',
      dataIndex: 'downloadUrl',
      key: 'downloadUrl',
      width: 400,
      render: (url: string, record: API.I18nPackageVersion) => {
        // 构建CDN链接
        const cdnUrl = `https://ec-cdn.blueorigin.cn/i18n/${record.device}/${record.fileName}`;
        return (
          <a href={cdnUrl} target="_blank" rel="noopener noreferrer">
            {cdnUrl}
          </a>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: (_: any, record: API.I18nPackageVersion) => (
        <Space>
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => window.open(record.downloadUrl, '_blank')}
          >
            下载
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 页面标题和操作 */}
        <Card>
          <Space align="center" style={{ width: '100%', justifyContent: 'space-between' }}>
            <div>
              <Title level={4} style={{ margin: 0 }}>
                语言包版本管理
              </Title>
              <Text type="secondary">
                管理和查看已导出的 i18n 语言包版本
              </Text>
            </div>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setExportModalVisible(true)}
              >
                导出新版本
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchPackageVersions}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Space>
        </Card>

        {/* 版本列表 */}
        <Card title="语言包版本列表">
          <Table
            columns={columns}
            dataSource={packageVersions}
            loading={loading}
            rowKey={(record) => `${record.device}-${record.version}`}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ x: 1800 }}
          />
        </Card>

        {/* 说明信息 */}
        <Card title="说明">
          <Space direction="vertical">
            <Text>
              <strong>文件路径规则：</strong>
            </Text>
            <Text>• Android JSON: i18n/android/版本号.json</Text>
            <Text>• iOS JSON: i18n/ios/版本号.json</Text>
            <Text>
              <strong>JSON 格式：</strong> 包含 meta 和 data 字段
            </Text>
            <Text>• meta: 包含版本、生成时间、命名空间、语言列表、App版本兼容性等元数据</Text>
            <Text>• data: 语言数据，格式为 map[languageCode]map[key]value</Text>
            <Text>
              <strong>Meta 信息说明：</strong>
            </Text>
            <Text>• 命名空间：语言包所属的应用或模块</Text>
            <Text>• 生成时间：语言包的实际生成时间</Text>
            <Text>• iOS/Android App 版本：指定此语言包适用的应用版本范围</Text>
            <Text type="secondary">
              语言包可通过导出功能直接生成，或通过 xxl-job 定时任务自动生成
            </Text>
          </Space>
        </Card>
      </Space>

      {/* 导出模态框 */}
      <Modal
        title="导出语言包"
        open={exportModalVisible}
        onCancel={() => {
          setExportModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={exportLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleTriggerExport}
          initialValues={{
            namespace: 'ohsome-app',
            languages: ['en-US', 'zh-CN'],
          }}
        >
          <Form.Item
            name="tag"
            label="版本标签"
            rules={[{ required: true, message: '请输入版本标签' }]}
          >
            <Input placeholder="例如: v1.0.0 或 20240101" />
          </Form.Item>

          <Form.Item
            name="namespace"
            label="命名空间"
            rules={[{ required: true, message: '请选择命名空间' }]}
          >
            <Select
              placeholder="请选择命名空间"
              options={I18nNamespaceOptions}
            />
          </Form.Item>

          <Form.Item
            name="languages"
            label="语言列表"
            rules={[{ required: true, message: '请选择至少一种语言' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择要导出的语言"
              options={LanguageOptions}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            name="iosAppVersions"
            label="适用的 iOS App 版本"
            tooltip="指定此语言包适用的 iOS App 版本列表"
          >
            <Select
              mode="tags"
              placeholder="例如: 1.0.0, 1.1.0"
              tokenSeparators={[',']}
            />
          </Form.Item>

          <Form.Item
            name="androidAppVersions"
            label="适用的 Android App 版本"
            tooltip="指定此语言包适用的 Android App 版本列表"
          >
            <Select
              mode="tags"
              placeholder="例如: 1.0.0, 1.1.0"
              tokenSeparators={[',']}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default PackageVersions;
