# 更新产品媒体类型功能

## 功能概述

此功能用于批量更新产品媒体记录的类型字段，将旧的媒体类型转换为新的 SCM 媒体类型：

- `gallery_image` → `scm_gallery_image`
- `white_image` → `scm_white_image`

如果媒体记录的 `media_hash` 字段为空，系统会自动下载媒体文件并计算 MD5 哈希值进行更新。

## 使用方法

### 1. 访问页面
在履约中心导航菜单中，选择：
**Maintenance** → **Update Product Media Type**

### 2. 填写表单
- **区域**：选择要处理的区域（如 ID、MY、SG、VN、TH 等）
- **产品媒体记录ID**：输入需要处理的产品媒体记录ID，每行一个

### 3. 开始处理
点击"开始处理"按钮，系统会逐个处理输入的媒体记录ID。

### 4. 查看结果
处理完成后，页面会显示处理状态表格，包含：
- 媒体记录ID
- 处理状态（成功/失败）
- 错误信息（如果处理失败）

## 处理逻辑

### 后端处理流程
1. 根据ID查询 `product_media` 记录
2. 检查记录是否已经处理过（media_type 以 `scm_` 开头）
3. 如果未处理，更新 media_type：
   - `gallery_image` → `scm_gallery_image`
   - `white_image` → `scm_white_image`
4. 如果 `media_hash` 为空：
   - 下载媒体文件
   - 计算 MD5 哈希值
   - 更新 `media_hash` 字段
5. 保存更新到数据库

### 跳过条件
- 记录不存在
- media_type 已经是 `scm_` 开头（已处理过）
- media_type 不是 `gallery_image` 或 `white_image`

## API 接口

### 请求
```
PATCH /api/regions/{region}/maintenance/product-media/{id}/media-type
```

### 请求参数
- `region` (路径参数): 区域代码
- `id` (路径参数): 产品媒体记录ID
- `mediaType` (请求体): 媒体类型（当前版本未使用）

### 响应
```json
{
  "success": boolean,
  "errorMessage": "string"
}
```

## 注意事项

1. **批量处理**：支持批量输入多个媒体记录ID，系统会逐个处理
2. **幂等性**：已处理过的记录会被跳过，不会重复处理
3. **错误处理**：单个记录处理失败不会影响其他记录的处理
4. **网络依赖**：如果需要计算 media_hash，系统会下载媒体文件，请确保网络连接正常
5. **权限要求**：需要相应的维护权限才能访问此功能

## 错误码说明

- `记录不存在`：指定的产品媒体记录ID不存在
- `已处理过`：该记录的 media_type 已经是 SCM 类型
- `不支持的媒体类型`：media_type 不是 gallery_image 或 white_image
- `文件下载失败`：无法下载媒体文件进行哈希计算
- `数据库更新失败`：更新数据库时发生错误

## 技术实现

### 前端技术栈
- React + TypeScript
- Ant Design Pro Components
- ProForm 表单组件
- ProTable 结果展示

### 后端技术栈
- Go-Zero 框架
- GORM 数据库操作
- HTTP 客户端文件下载
- MD5 哈希计算

### 相关文件
- 前端组件：`web/src/pages/FulfillmentCenter/Maintenance/UpdateProductMediaType/index.tsx`
- API 服务：`web/src/services/ant-design-pro/api.ts`
- 类型定义：`web/src/services/ant-design-pro/typings.d.ts`
- 后端逻辑：`webapi/internal/logic/fulfillment_center/updateProductMediaTypeLogic.go`
- API 定义：`webapi/desc/fulfillment.api`
- 路由配置：`web/config/routes.ts`
