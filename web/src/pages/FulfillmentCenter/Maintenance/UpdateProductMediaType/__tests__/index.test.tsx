import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import UpdateProductMediaType from '../index';
import * as api from '@/services/ant-design-pro/api';

// Mock the API
jest.mock('@/services/ant-design-pro/api');
const mockUpdateProductMediaType = api.updateProductMediaType as jest.MockedFunction<typeof api.updateProductMediaType>;

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

describe('UpdateProductMediaType', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    render(<UpdateProductMediaType />);
    
    expect(screen.getByText('更新产品媒体类型')).toBeInTheDocument();
    expect(screen.getByText('此功能用于批量更新产品媒体记录的类型字段。')).toBeInTheDocument();
    expect(screen.getByText('• gallery_image → scm_gallery_image')).toBeInTheDocument();
    expect(screen.getByText('• white_image → scm_white_image')).toBeInTheDocument();
  });

  it('shows validation error when required fields are missing', async () => {
    render(<UpdateProductMediaType />);
    
    const submitButton = screen.getByText('开始处理');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请选择区域')).toBeInTheDocument();
      expect(screen.getByText('请输入产品媒体记录ID')).toBeInTheDocument();
    });
  });

  it('processes media IDs successfully', async () => {
    mockUpdateProductMediaType.mockResolvedValue({
      success: true,
      errorMessage: '',
    });

    render(<UpdateProductMediaType />);
    
    // Fill in the form
    const regionSelect = screen.getByLabelText('区域');
    fireEvent.mouseDown(regionSelect);
    const regionOption = screen.getByText('Indonesia');
    fireEvent.click(regionOption);

    const mediaIdsTextarea = screen.getByLabelText('产品媒体记录ID');
    fireEvent.change(mediaIdsTextarea, { target: { value: '12345\n67890' } });

    const submitButton = screen.getByText('开始处理');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUpdateProductMediaType).toHaveBeenCalledTimes(2);
      expect(mockUpdateProductMediaType).toHaveBeenCalledWith({
        region: 'ID',
        id: '12345',
        mediaType: '',
      });
      expect(mockUpdateProductMediaType).toHaveBeenCalledWith({
        region: 'ID',
        id: '67890',
        mediaType: '',
      });
    });

    await waitFor(() => {
      expect(message.success).toHaveBeenCalledWith('全部处理成功，共 2 个媒体记录');
    });
  });

  it('handles API errors correctly', async () => {
    mockUpdateProductMediaType.mockRejectedValue(new Error('Network error'));

    render(<UpdateProductMediaType />);
    
    // Fill in the form
    const regionSelect = screen.getByLabelText('区域');
    fireEvent.mouseDown(regionSelect);
    const regionOption = screen.getByText('Indonesia');
    fireEvent.click(regionOption);

    const mediaIdsTextarea = screen.getByLabelText('产品媒体记录ID');
    fireEvent.change(mediaIdsTextarea, { target: { value: '12345' } });

    const submitButton = screen.getByText('开始处理');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('全部处理失败，共 1 个媒体记录');
    });
  });

  it('displays processing results in table', async () => {
    mockUpdateProductMediaType
      .mockResolvedValueOnce({ success: true, errorMessage: '' })
      .mockResolvedValueOnce({ success: false, errorMessage: '记录不存在' });

    render(<UpdateProductMediaType />);
    
    // Fill in the form
    const regionSelect = screen.getByLabelText('区域');
    fireEvent.mouseDown(regionSelect);
    const regionOption = screen.getByText('Indonesia');
    fireEvent.click(regionOption);

    const mediaIdsTextarea = screen.getByLabelText('产品媒体记录ID');
    fireEvent.change(mediaIdsTextarea, { target: { value: '12345\n67890' } });

    const submitButton = screen.getByText('开始处理');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('处理状态')).toBeInTheDocument();
      expect(screen.getByText('12345')).toBeInTheDocument();
      expect(screen.getByText('67890')).toBeInTheDocument();
      expect(screen.getByText('成功')).toBeInTheDocument();
      expect(screen.getByText('失败')).toBeInTheDocument();
      expect(screen.getByText('记录不存在')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(message.warning).toHaveBeenCalledWith('处理完成：成功 1 个，失败 1 个');
    });
  });
});
