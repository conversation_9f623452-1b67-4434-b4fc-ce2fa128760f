import React, { useState } from 'react';
import { Card, message, Table, Tag } from 'antd';
import {
  PageContainer,
  ProForm,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { updateProductMediaType } from '@/services/ant-design-pro/api';
import { RegionEnum } from '@/constants/enum';

interface MediaStatus {
  id: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
}

const UpdateProductMediaType: React.FC = () => {
  const [mediaList, setMediaList] = useState<MediaStatus[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (values: any) => {
    const mediaIds = values.mediaIds.split('\n').filter((id: string) => id.trim());

    // 初始化媒体状态
    const initialMediaList = mediaIds.map((id: string) => ({
      id: id.trim(),
      status: 'pending' as const,
      error: '',
    }));
    setMediaList(initialMediaList);
    setIsProcessing(true);

    // 逐个处理媒体记录
    const results: MediaStatus[] = [];

    for (const mediaId of mediaIds) {
      const trimmedId = mediaId.trim();
      if (!trimmedId) continue;

      try {
        const response = await updateProductMediaType({
          region: values.region,
          id: trimmedId,
          mediaType: '', // 当前版本未使用此字段
        });

        // 根据 API 响应判断成功或失败
        if (response.success !== false) {
          results.push({
            id: trimmedId,
            status: 'success',
          });
        } else {
          results.push({
            id: trimmedId,
            status: 'error',
            error: response.errorMessage || '处理失败',
          });
        }
      } catch (error: any) {
        results.push({
          id: trimmedId,
          status: 'error',
          error: error?.message || '请求失败',
        });
      }
    }

    setMediaList(results);
    
    // 统计结果
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    if (errorCount === 0) {
      message.success(`全部处理成功，共 ${successCount} 个媒体记录`);
    } else if (successCount === 0) {
      message.error(`全部处理失败，共 ${errorCount} 个媒体记录`);
    } else {
      message.warning(`处理完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
    }
    
    setIsProcessing(false);
  };

  const columns = [
    {
      title: '媒体记录ID',
      dataIndex: 'id',
      key: 'id',
      width: '30%',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '20%',
      render: (status: string) => {
        const colorMap = {
          pending: 'blue',
          success: 'green',
          error: 'red',
        };
        const textMap = {
          pending: '处理中',
          success: '成功',
          error: '失败',
        };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{textMap[status as keyof typeof textMap]}</Tag>;
      },
    },
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      width: '50%',
      render: (error: string) => (
        <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
          {error}
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card title="更新产品媒体类型">
        <div style={{ marginBottom: 16, color: '#666', fontSize: '14px' }}>
          <p>此功能用于批量更新产品媒体记录的类型字段。</p>
          <p>• gallery_image → scm_gallery_image</p>
          <p>• white_image → scm_white_image</p>
          <p>• 如果 media_hash 为空，系统会自动计算并更新</p>
          <p>• 已处理过的记录（scm_* 开头）会被跳过</p>
          <p>请输入需要处理的产品媒体记录ID，每行一个。</p>
        </div>
        <ProForm
          onFinish={handleSubmit}
          grid={true}
          layout="horizontal"
          submitter={{
            submitButtonProps: {
              loading: isProcessing,
            },
            searchConfig: {
              submitText: '开始处理',
            },
          }}
        >
          <ProFormSelect
            name="region"
            label="区域"
            placeholder="请选择区域"
            rules={[{ required: true, message: '请选择区域' }]}
            colProps={{ span: 6, style: { marginBottom: 0 } }}
            valueEnum={RegionEnum}
          />
          <ProFormTextArea
            name="mediaIds"
            label="产品媒体记录ID"
            placeholder="请输入产品媒体记录ID，每行一个&#10;例如：&#10;12345&#10;67890&#10;11111"
            rules={[{ required: true, message: '请输入产品媒体记录ID' }]}
            colProps={{ span: 24, style: { marginBottom: 0 } }}
            fieldProps={{
              autoSize: { minRows: 4, maxRows: 8 },
            }}
          />
        </ProForm>
      </Card>
      {mediaList.length > 0 && (
        <Card title="处理状态" style={{ marginTop: 16 }}>
          <Table
            columns={columns}
            dataSource={mediaList}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      )}
    </PageContainer>
  );
};

export default UpdateProductMediaType;
