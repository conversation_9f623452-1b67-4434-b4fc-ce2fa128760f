# GORM DAL Generator API

## Overview

This API endpoint allows you to generate GORM DAL (Data Access Layer) code for specified databases and automatically push the generated code to a GitLab repository.

## Endpoint

```
POST /api/ops/gorm/dal/generate
```

## Authentication

This endpoint requires authentication via the `AuthInterceptor` middleware.

## Request

### Headers
- `Content-Type: application/json`
- `Authorization: Bearer <your-token>`

### Body
```json
{
  "databaseNames": ["database1", "database2", "database3"]
}
```

#### Parameters
- `databaseNames` (array of strings, required): List of site names for which to generate DAL code. Valid values are:
  - `ops` - Operations platform database
  - `fulfillment` - Fulfillment center database
  - `ohsome` - Independent site database

## Response

### Success Response
```json
{
  "success": true,
  "errorMessage": "DAL code generated and pushed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "errorMessage": "Error description here"
}
```

## How It Works

1. **Repository Management**: The API clones or pulls the latest code from the GitLab repository `https://git.blueorigin.work/e-commerce/gorm-generated-dal`

2. **Code Generation**: For each specified site, the API:
   - Implements the GORM generation logic directly (based on `scripts/gormgen`)
   - Creates the directory structure `${site_name}/dal/query` and `${site_name}/dal/model`
   - Connects to the site-specific database using `datasource.GetDsSiteDefault(siteName)`
   - Queries all tables in the current database using `SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE()`
   - Generates GORM models and query interfaces for all tables in the site's database
   - Creates model slice aliases for better type safety
   - Generates a `dal.gen.go` file with locale-aware query initialization
   - Generates enhanced query files (`*.enhanced.go`) for custom query methods
   - Converts query files to use slice aliases (replaces `[]*model.XXX` with `model.XXXSlice`)

3. **GitLab API Integration**: After generation:
   - Creates a local working directory for code generation
   - Uses GitLab API to commit all generated files with a timestamped message
   - Pushes directly to the `main` branch without requiring git commands
   - Works in containerized environments without git installation

## Technical Implementation

### Backend Implementation

The API is implemented in `webapi/internal/logic/ops/generateGormDalAndPushToGitlabLogic.go` with the following key components:

#### Template System
- **Embedded Templates**: Uses Go's `embed` feature to include template files directly in the binary
- **Template Files**: Located in `webapi/internal/logic/ops/templates/`
  - `gen_dal.tpl`: Main DAL initialization template
  - `gen_enhanced.tpl`: Enhanced query structure template
  - `default_queries.tpl`: Common CRUD operations template (for future use)
- **Template Functions**: Supports custom functions like `toSmallCamelCase` for naming conversions

#### Code Generation Process
1. **Database Connection**: Uses `datasource.GetDsSiteDefault(siteName)` for site-specific connections
2. **Table Discovery**: Queries `information_schema.tables` to find all tables
3. **GORM Generation**: Uses `gorm.io/gen` to generate models and queries
4. **Concurrent Post-processing**: Uses `golang.org/x/sync/errgroup` for parallel execution:
   - **Slice Aliases Generation**: Creates type-safe slice aliases
   - **DAL File Generation**: Generates main DAL initialization file
   - **Enhanced Query Generation**: Creates enhanced query files (concurrent per file)
5. **Query Conversion**: Converts generated query files to use slice aliases (concurrent per file)
6. **Template Rendering**: Uses embedded templates for consistent code generation
7. **GitLab API Integration**: Uses GitLab REST API for repository operations instead of git commands

#### Performance Optimizations
- **Concurrent Post-processing**: 3 parallel tasks (slice aliases, DAL file, enhanced queries)
- **Concurrent Enhanced Generation**: Up to 5 files processed simultaneously
- **Concurrent Query Conversion**: Up to 10 AST transformations in parallel
- **Controlled Concurrency**: Uses `errgroup.SetLimit()` to prevent resource exhaustion

## Configuration

The API uses the GitLab configuration from `webapi/internal/config/config.go`:

```yaml
GitLab:
  Host: https://git.blueorigin.work
  Token: your-gitlab-token
```

## Example Usage

### cURL
```bash
curl -X POST "http://localhost:8888/api/ops/gorm/dal/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-auth-token" \
  -d '{
    "databaseNames": ["ops", "fulfillment", "ohsome"]
  }'
```

### JavaScript/Fetch
```javascript
fetch('/api/ops/gorm/dal/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-auth-token'
  },
  body: JSON.stringify({
    databaseNames: ['ops', 'fulfillment', 'ohsome']
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Error Handling

Common error scenarios:

1. **Missing GitLab Token**: If the GitLab token is not configured in the config file
2. **Repository Access Issues**: If the GitLab repository cannot be accessed or cloned
3. **Code Generation Failures**: If the GORM generation process fails for a specific database
4. **Git Operation Failures**: If commit or push operations fail

## Notes

- The generated code follows the structure defined in `scripts/gormgen`
- Each database gets its own directory in the target repository
- The API continues processing other databases even if one fails
- All git operations use the configured GitLab token for authentication
- The temporary working directory is `/tmp/dal-gen`

## Dependencies

- Requires access to the default database connection (via `webapi/datasource`)
- Requires the following Go packages:
  - `gorm.io/gen` for GORM code generation
  - `gorm.io/gorm` for database operations
  - `golang.org/x/tools/go/packages` for Go code analysis
- Requires git to be installed on the server
- Requires network access to the GitLab repository
- Requires valid GitLab token with push permissions to the target repository

## Technical Implementation

The implementation directly uses GORM Gen library to:

1. **Site-Specific Database Connection**: Uses `datasource.GetDsSiteDefault(siteName)` to get the site-specific database connection
2. **Table Discovery**: Queries `information_schema.tables` using `DATABASE()` function to find all tables in the current database
3. **Code Generation**: Uses `gen.NewGenerator()` with the following configuration:
   - `OutPath`: `${site_name}/dal/query` for CRUD operations
   - `ModelPkgPath`: `${site_name}/dal/model` for model definitions
   - `FieldWithTypeTag`: true for JSON/DB tags
   - `Mode`: `gen.WithDefaultQuery | gen.WithQueryInterface` for comprehensive query methods
4. **Additional Files**:
   - Generates slice aliases for each model (e.g., `slice_user.go` containing `UserSlice []*User`)
   - Creates `dal.gen.go` with locale-aware query initialization
   - Generates enhanced query files (e.g., `user.enhanced.go`) with custom query structures
   - Supports multi-region database access through locale context

## Generated File Structure

For each site, the following files are generated:

```
${site_name}/dal/
├── dal.gen.go                    # Main DAL initialization file
├── model/                        # GORM model definitions
│   ├── user.gen.go              # User model
│   ├── order.gen.go             # Order model
│   ├── slice_user.go            # User slice alias
│   ├── slice_order.go           # Order slice alias
│   └── ...                      # Other models and slice aliases
├── query/                        # GORM query interfaces
│   ├── gen.go                   # Query initialization
│   ├── user.gen.go              # User queries
│   ├── order.gen.go             # Order queries
│   └── ...                      # Other queries
├── user.enhanced.go             # Enhanced User queries
├── order.enhanced.go            # Enhanced Order queries
└── ...                          # Other enhanced queries
```

## Enhanced Query Files

Each enhanced query file provides:

- **Custom Query Structure**: `${ModelName}Do` struct with context and query access
- **Enhanced Constructor**: `Get${ModelName}Enhanced(ctx)` function
- **Template for Custom Methods**: Examples of how to add custom query methods
- **Usage Examples**: Both standard GORM Gen usage and enhanced query usage

## Slice Alias Files

Each model gets a corresponding slice alias file that provides type-safe slice operations:

```go
// slice_user.go
package model
type UserSlice []*User
```

**Benefits:**
- **Type Safety**: Provides compile-time type checking for slice operations
- **Code Clarity**: Makes slice types explicit and self-documenting
- **IDE Support**: Better autocomplete and refactoring support
- **Consistency**: Standardized naming across all models

**Usage Example:**
```go
var users model.UserSlice
users = append(users, &model.User{Name: "John"})
// Type-safe operations on user slices
```

## Query File Conversion

The generator automatically converts all generated query files to use slice aliases instead of raw slice types:

**Before Conversion:**
```go
func (u userDo) Find() ([]*model.User, error) {
    result, err := u.DO.Find()
    return result.([]*model.User), err
}

func (u userDo) CreateInBatches(values []*model.User, batchSize int) error {
    return u.DO.CreateInBatches(values, batchSize)
}
```

**After Conversion:**
```go
func (u userDo) Find() (model.UserSlice, error) {
    result, err := u.DO.Find()
    if err != nil {
        return model.UserSlice{}, err
    }
    if slice, ok := result.([]*model.User); ok {
        return model.UserSlice(slice), err
    }
    return model.UserSlice{}, err
}

func (u userDo) CreateInBatches(values model.UserSlice, batchSize int) error {
    return u.DO.CreateInBatches(values, batchSize)
}
```

**Benefits:**
- **Type Safety**: Compile-time type checking for all slice operations
- **Error Handling**: Proper error handling with early returns
- **Type Assertion Safety**: Safe type assertions with fallback handling
- **Consistency**: Uniform slice types across the entire codebase
- **Maintainability**: Easier refactoring and code navigation
- **Performance**: No runtime overhead, purely compile-time type aliases

**Technical Implementation:**
- **Direct Code Migration**: Core logic directly ported from `scripts/gormgen/convert/convert_query.go`
- **AST Processing**: Uses Go's AST (Abstract Syntax Tree) parsing for precise code transformation
- **Type Replacement**: Replaces `[]*model.XXX` patterns with `model.XXXSlice` in function signatures
- **Method Rewriting**: Completely rewrites Find method implementations with proper error handling
- **Import Management**: Automatically adds necessary imports (like `fmt`) when needed
- **Code Preservation**: Maintains original code structure, comments, and formatting

## GitLab API Integration

The generator uses GitLab's REST API for repository operations, eliminating the need for git commands:

**API Endpoints Used:**
- `GET /projects/{id}/repository/tree?path={dir}&ref=main` - Check directory existence
- `POST /projects/{id}/repository/commits` - Create commits with multiple file changes
  - **Delete commit** (optional): Uses `"action": "delete"` for existing site directories
  - **Create commit**: Uses `"action": "create"` for all new files

**Benefits:**
- **Container Friendly**: Works in environments without git installation
- **Authentication**: Uses GitLab tokens for secure API access
- **Batch Operations**: Commits all files in a single API call
- **Error Handling**: Detailed error messages from GitLab API responses

**Commit Structure:**
```json
{
  "branch": "main",
  "commit_message": "auto: update dal code [2024-01-01 12:00:00]",
  "actions": [
    {
      "action": "update",
      "file_path": "ops/dal/model/user.gen.go",
      "content": "package model\n\ntype User struct {...}"
    }
  ]
}
```

**Ultra-Simple GitLab Strategy:**
1. **Delete-and-Update Strategy**:
   - Identifies site directories from file paths (e.g., `ops/`, `admin/`)
   - Gets list of existing files in those directories
   - Deletes existing files in first commit (must succeed)
   - Updates all files with `"action": "update"` in second commit
   - **2-3 API calls total**: N directory queries + 1 delete + 1 update
   - **Fail-fast approach**: Delete failures stop the process (no point continuing)
2. **UPDATE Action Only**: Always uses `"action": "update"` for all files
   - GitLab API creates files if they don't exist
   - Updates files if they already exist
   - No need to check file existence
3. **Clean Commit History**: Always creates exactly 2 commits maximum
4. **Simple and Reliable**: Delete must succeed, then update always works

## Site Configuration

The API supports the following sites as defined in `webapi/etc/webapi.yaml`:

- **ops**: Operations platform (`bo_service_data_ops`)
- **fulfillment**: Fulfillment center with multi-region support:
  - ID: `bo_service_ec`
  - MY: `bo_service_ec_my_test`
  - SG: `bo_service_ec_sg_test`
  - VN: `bo_service_ec_vn_test`
  - TH: `bo_service_ec_th_test`
  - KH: `bo_service_ec_kh_test`
- **ohsome**: Independent site with multi-region support:
  - ID: `shop_mall`
  - MY: `my_shop_mall`
  - SG: `sg_shop_mall`
  - VN: `vn_shop_mall`
  - KH: `kh_shop_mall`
